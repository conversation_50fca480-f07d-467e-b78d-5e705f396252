#include <stdio.h>
#include <string.h>
#include "buffer_overflow.h"
#include "memory_leak.h"

// CWE-120: Buffer Copy without Checking Size of Input
void process_user_input(char* input) {
    char buffer[64];
    strcpy(buffer, input);  // 危险：未检查输入长度，可能导致缓冲区溢出
    printf("Processed: %s\n", buffer);
    
    // 调用memory_leak.c中的函数
    store_data(buffer);
}

char* get_user_data(void) {
    static char data[128] = "Sample user data";
    return data;
}
