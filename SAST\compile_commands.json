[{"directory": "/root/preprocess/SAST", "command": "/usr/bin/c++ -o CMakeFiles/pvs-studio-project.dir/buffer_overflow.c.o -c /root/preprocess/SAST/repo/buffer_overflow.c", "file": "/root/preprocess/SAST/repo/buffer_overflow.c"}, {"directory": "/root/preprocess/SAST", "command": "/usr/bin/c++ -o CMakeFiles/pvs-studio-project.dir/format_string.c.o -c /root/preprocess/SAST/repo/format_string.c", "file": "/root/preprocess/SAST/repo/format_string.c"}, {"directory": "/root/preprocess/SAST", "command": "/usr/bin/c++ -o CMakeFiles/pvs-studio-project.dir/integer_overflow.c.o -c /root/preprocess/SAST/repo/integer_overflow.c", "file": "/root/preprocess/SAST/repo/integer_overflow.c"}, {"directory": "/root/preprocess/SAST", "command": "/usr/bin/c++ -o CMakeFiles/pvs-studio-project.dir/main.c.o -c /root/preprocess/SAST/repo/main.c", "file": "/root/preprocess/SAST/repo/main.c"}, {"directory": "/root/preprocess/SAST", "command": "/usr/bin/c++ -o CMakeFiles/pvs-studio-project.dir/memory_leak.c.o -c /root/preprocess/SAST/repo/memory_leak.c", "file": "/root/preprocess/SAST/repo/memory_leak.c"}, {"directory": "/root/preprocess/SAST", "command": "/usr/bin/c++ -o CMakeFiles/pvs-studio-project.dir/null_pointer.c.o -c /root/preprocess/SAST/repo/null_pointer.c", "file": "/root/preprocess/SAST/repo/null_pointer.c"}]