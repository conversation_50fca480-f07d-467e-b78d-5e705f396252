
if(NOT "/root/preprocess/SAST/build/_deps/pvs_cmakemodule-subbuild/pvs_cmakemodule-populate-prefix/src/pvs_cmakemodule-populate-stamp/pvs_cmakemodule-populate-gitinfo.txt" IS_NEWER_THAN "/root/preprocess/SAST/build/_deps/pvs_cmakemodule-subbuild/pvs_cmakemodule-populate-prefix/src/pvs_cmakemodule-populate-stamp/pvs_cmakemodule-populate-gitclone-lastrun.txt")
  message(STATUS "Avoiding repeated git clone, stamp file is up to date: '/root/preprocess/SAST/build/_deps/pvs_cmakemodule-subbuild/pvs_cmakemodule-populate-prefix/src/pvs_cmakemodule-populate-stamp/pvs_cmakemodule-populate-gitclone-lastrun.txt'")
  return()
endif()

execute_process(
  COMMAND ${CMAKE_COMMAND} -E rm -rf "/root/preprocess/SAST/build/_deps/pvs_cmakemodule-src"
  RESULT_VARIABLE error_code
  )
if(error_code)
  message(FATAL_ERROR "Failed to remove directory: '/root/preprocess/SAST/build/_deps/pvs_cmakemodule-src'")
endif()

# try the clone 3 times in case there is an odd git clone issue
set(error_code 1)
set(number_of_tries 0)
while(error_code AND number_of_tries LESS 3)
  execute_process(
    COMMAND "/usr/bin/git"  clone --no-checkout --config "advice.detachedHead=false" "https://github.com/viva64/pvs-studio-cmake-module.git" "pvs_cmakemodule-src"
    WORKING_DIRECTORY "/root/preprocess/SAST/build/_deps"
    RESULT_VARIABLE error_code
    )
  math(EXPR number_of_tries "${number_of_tries} + 1")
endwhile()
if(number_of_tries GREATER 1)
  message(STATUS "Had to git clone more than once:
          ${number_of_tries} times.")
endif()
if(error_code)
  message(FATAL_ERROR "Failed to clone repository: 'https://github.com/viva64/pvs-studio-cmake-module.git'")
endif()

execute_process(
  COMMAND "/usr/bin/git"  checkout master --
  WORKING_DIRECTORY "/root/preprocess/SAST/build/_deps/pvs_cmakemodule-src"
  RESULT_VARIABLE error_code
  )
if(error_code)
  message(FATAL_ERROR "Failed to checkout tag: 'master'")
endif()

set(init_submodules TRUE)
if(init_submodules)
  execute_process(
    COMMAND "/usr/bin/git"  submodule update --recursive --init 
    WORKING_DIRECTORY "/root/preprocess/SAST/build/_deps/pvs_cmakemodule-src"
    RESULT_VARIABLE error_code
    )
endif()
if(error_code)
  message(FATAL_ERROR "Failed to update submodules in: '/root/preprocess/SAST/build/_deps/pvs_cmakemodule-src'")
endif()

# Complete success, update the script-last-run stamp file:
#
execute_process(
  COMMAND ${CMAKE_COMMAND} -E copy
    "/root/preprocess/SAST/build/_deps/pvs_cmakemodule-subbuild/pvs_cmakemodule-populate-prefix/src/pvs_cmakemodule-populate-stamp/pvs_cmakemodule-populate-gitinfo.txt"
    "/root/preprocess/SAST/build/_deps/pvs_cmakemodule-subbuild/pvs_cmakemodule-populate-prefix/src/pvs_cmakemodule-populate-stamp/pvs_cmakemodule-populate-gitclone-lastrun.txt"
  RESULT_VARIABLE error_code
  )
if(error_code)
  message(FATAL_ERROR "Failed to copy script-last-run stamp file: '/root/preprocess/SAST/build/_deps/pvs_cmakemodule-subbuild/pvs_cmakemodule-populate-prefix/src/pvs_cmakemodule-populate-stamp/pvs_cmakemodule-populate-gitclone-lastrun.txt'")
endif()

