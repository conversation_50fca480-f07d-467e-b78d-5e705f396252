# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /root/preprocess/SAST

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /root/preprocess/SAST/build

# Utility rule file for example1.analyze-pvs_analysis.xml-log.

# Include any custom commands dependencies for this target.
include CMakeFiles/example1.analyze-pvs_analysis.xml-log.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/example1.analyze-pvs_analysis.xml-log.dir/progress.make

CMakeFiles/example1.analyze-pvs_analysis.xml-log: PVS-Studio/pvs-studio-cmake-example-1/main.cpp.log
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --blue --bold --progress-dir=/root/preprocess/SAST/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Generating pvs_analysis.xml"
	cat /root/preprocess/SAST/build/PVS-Studio/pvs-studio-cmake-example-1/main.cpp.log > /root/preprocess/SAST/build/pvs_analysis.xml 2>/dev/null || true
	/usr/bin/cmake -E remove -f /root/preprocess/SAST/build/pvs_analysis.xml.pvs.raw
	/usr/bin/cmake -E rename /root/preprocess/SAST/build/pvs_analysis.xml /root/preprocess/SAST/build/pvs_analysis.xml.pvs.raw
	/usr/bin/plog-converter --stdout -t xml /root/preprocess/SAST/build/pvs_analysis.xml.pvs.raw -o /root/preprocess/SAST/build/pvs_analysis.xml -a GA:1,2
	/usr/bin/cmake -E remove -f /root/preprocess/SAST/build/pvs_analysis.xml.pvs.raw

PVS-Studio/pvs-studio-cmake-example-1/main.cpp.log: ../main.cpp
PVS-Studio/pvs-studio-cmake-example-1/main.cpp.log: ../main.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --blue --bold --progress-dir=/root/preprocess/SAST/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Analyzing CXX file main.cpp"
	/usr/bin/cmake -E make_directory /root/preprocess/SAST/build/PVS-Studio/pvs-studio-cmake-example-1
	/usr/bin/cmake -E remove_directory /root/preprocess/SAST/build/PVS-Studio/pvs-studio-cmake-example-1/main.cpp.log
	/usr/bin/cmake -D PVS_STUDIO_AS_SCRIPT=TRUE -D "PVS_STUDIO_COMMAND=/usr/bin/pvs-studio-analyzer;analyze;--output-file;/root/preprocess/SAST/build/PVS-Studio/pvs-studio-cmake-example-1/main.cpp.log;--source-file;/root/preprocess/SAST/main.cpp;--platform;linux64;--preprocessor;gcc;--cxx;/usr/bin/c++;-a;GA;--cl-params;;;;;-DPVS_STUDIO;/root/preprocess/SAST/main.cpp" -D PVS_STUDIO_LOG_FILE=/root/preprocess/SAST/build/PVS-Studio/pvs-studio-cmake-example-1/main.cpp.log -P /root/preprocess/SAST/build/_deps/pvs_cmakemodule-src/PVS-Studio.cmake

example1.analyze-pvs_analysis.xml-log: CMakeFiles/example1.analyze-pvs_analysis.xml-log
example1.analyze-pvs_analysis.xml-log: PVS-Studio/pvs-studio-cmake-example-1/main.cpp.log
example1.analyze-pvs_analysis.xml-log: CMakeFiles/example1.analyze-pvs_analysis.xml-log.dir/build.make
.PHONY : example1.analyze-pvs_analysis.xml-log

# Rule to build all files generated by this target.
CMakeFiles/example1.analyze-pvs_analysis.xml-log.dir/build: example1.analyze-pvs_analysis.xml-log
.PHONY : CMakeFiles/example1.analyze-pvs_analysis.xml-log.dir/build

CMakeFiles/example1.analyze-pvs_analysis.xml-log.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/example1.analyze-pvs_analysis.xml-log.dir/cmake_clean.cmake
.PHONY : CMakeFiles/example1.analyze-pvs_analysis.xml-log.dir/clean

CMakeFiles/example1.analyze-pvs_analysis.xml-log.dir/depend:
	cd /root/preprocess/SAST/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /root/preprocess/SAST /root/preprocess/SAST /root/preprocess/SAST/build /root/preprocess/SAST/build /root/preprocess/SAST/build/CMakeFiles/example1.analyze-pvs_analysis.xml-log.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : CMakeFiles/example1.analyze-pvs_analysis.xml-log.dir/depend

