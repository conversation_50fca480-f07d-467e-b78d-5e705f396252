{"cells": [{"cell_type": "code", "execution_count": 8, "id": "b49aa270-fd0c-4136-b2fb-2321f06cf000", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["import json\n", "from transformers import RobertaTokenizer, RobertaModel\n", "import torch\n", "\n", "# 加载 CodeBERT 模型\n", "MODEL_NAME = \"../codebert-base\"\n", "tokenizer = RobertaTokenizer.from_pretrained(MODEL_NAME)\n", "model = RobertaModel.from_pretrained(MODEL_NAME)\n"]}, {"cell_type": "code", "execution_count": null, "id": "d0fe6603-daa8-44d1-a8fe-f0e2ff89dda5", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Processing Entries: 100%|██████████| 192146/192146 [1:54:00<00:00, 28.09entry/s]  \n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "嵌入已添加并保存到文件：analyzed_files_with_embeddings.json\n"]}], "source": ["from tqdm import tqdm\n", "\n", "def add_codebert_embeddings_to_json(json_file_path, output_json_file_path):\n", "\n", "    with open(json_file_path, 'r', encoding='utf-8') as file:\n", "        data = json.load(file)  # 解析 JSON 数据\n", "\n", "       \n", "        if not isinstance(data, list):\n", "            print(\"输入 JSON 文件的数据类型错误，期望是一个列表！\")\n", "            return\n", "\n", "        with tqdm(total=len(data), desc=\"Processing Entries\", unit=\"entry\") as pbar:\n", "\n", "            for i, item in enumerate(data):\n", "                if \"code\" not in item:\n", "                    pbar.update(1)  # 无代码字段，进度条更新\n", "                    continue  # 跳过没有 code 字段的条目\n", "\n", "                code_snippet = item[\"code\"]\n", "\n", "                # 使用 CodeBERT Tokenizer 对代码进行编码\n", "                inputs = tokenizer(\n", "                    code_snippet,\n", "                    return_tensors=\"pt\",\n", "                    padding=True,\n", "                    truncation=True,\n", "                    max_length=512  # 限制最大 512 Token\n", "                )\n", "\n", "                # 通过 CodeBERT 模型生成嵌入\n", "                with torch.no_grad():\n", "                    outputs = model(**inputs)\n", "                    embedding = outputs.last_hidden_state[:, 0, :].squeeze()  # 提取 [CLS] token 的嵌入\n", "\n", "                # 将嵌入转换为 Python 列表，并添加到条目\n", "                item[\"codebert_embedding\"] = embedding.tolist()\n", "\n", "                # 更新进度条\n", "                pbar.update(1)\n", "\n", "    # 将更新后的数据保存到新的 JSON 文件中\n", "    with open(output_json_file_path, 'w', encoding='utf-8') as file:\n", "        json.dump(data, file, ensure_ascii=False, indent=4)\n", "        print(f\"\\n嵌入已添加并保存到文件：{output_json_file_path}\")\n", "\n", "\n", "input_json_file_path = \"analyzed_files.json\"  # 输入 JSON 文件路径\n", "output_json_file_path = \"analyzed_files_with_embeddings.json\"  # 输出 JSON 文件路径\n", "add_codebert_embeddings_to_json(input_json_file_path, output_json_file_path)"]}, {"cell_type": "code", "execution_count": null, "id": "81901a40-a3b0-4d13-9d58-93caff72f8f7", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.8"}}, "nbformat": 4, "nbformat_minor": 5}