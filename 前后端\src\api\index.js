import axios from 'axios'

const API_BASE_URL = 'http://localhost:3001/api'

const api = {
    // 上传并解压文件
    async uploadAndExtract(file) {
        const formData = new FormData()
        formData.append('file', file)

        const response = await axios.post(`${API_BASE_URL}/upload`, formData, {
            headers: {
                'Content-Type': 'multipart/form-data'
            },
            timeout: 60000 // 60秒超时
        })

        return response.data
    },
async extractFeatures(code, extractPath) {
        try {
            const response = await fetch(`${API_BASE_URL}/api/feature-extraction`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    code,
                    extractPath
                })
            });
            
            const data = await response.json();
            return data;
        } catch (error) {
            console.error('Feature extraction API call failed:', error);
            return {
                success: false,
                error: error.message,
                data: { features: {} }
            };
        }
    },
async analyzeLLM(repoPath, filePath, action = 'prompt') {
    try {
        const response = await axios.post(`${API_BASE_URL}/analyze-comprehensive`, {
            extractPath: repoPath,  // 改为 extractPath
            filePath: filePath,     // 保持 filePath
            action: action
        }, {
            timeout: 120000
        })

        return response.data
    } catch (error) {
        console.error('LLM analysis API call failed:', error);
        return {
            success: false,
            error: error.response?.data?.error || error.message,
            data: null
        }
    }
},
// 在 api/index.js 文件中添加以下方法

// 获取 LLM 响应文件列表
async getLLMResponseFiles(extractPath, fileName) {
    try {
        const response = await axios.get(`${API_BASE_URL}/llm-response-files`, {
            params: { 
                extractPath,
                fileName 
            }
        })
        return response.data
    } catch (error) {
        console.error('Get LLM response files failed:', error)
        return {
            success: false,
            error: error.message,
            data: { files: [] }
        }
    }
},

// 加载 LLM 响应文件内容
async loadLLMResponse(filePath) {
    try {
        const response = await axios.get(`${API_BASE_URL}/load-llm-response`, {
            params: { path: filePath }
        })
        return response.data
    } catch (error) {
        console.error('Load LLM response failed:', error)
        return {
            success: false,
            error: error.message,
            data: null
        }
    }
},
async getRepositories() {
    try {
        const response = await axios.get(`${API_BASE_URL}/repositories`)
        return response.data
    } catch (error) {
        console.error('获取仓库列表失败:', error)
        return {
            success: false,
            error: error.message,
            data: []
        }
    }
},

// 获取特定仓库的信息
async getRepository(name) {
    try {
        const response = await axios.get(`${API_BASE_URL}/repository/${name}`)
        return response.data
    } catch (error) {
        console.error('获取仓库信息失败:', error)
        return {
            success: false,
            error: error.message
        }
    }
},

// 删除仓库
async deleteRepository(name) {
    try {
        const response = await axios.delete(`${API_BASE_URL}/repository/${name}`)
        return response.data
    } catch (error) {
        console.error('删除仓库失败:', error)
        return {
            success: false,
            error: error.message
        }
    }
},
    async analyzeRepository(extractPath) {
        try {
            const response = await axios.post(`${API_BASE_URL}/analyze-repository`, {
                extractPath: extractPath,
                clearDb: true  // 可选参数
            })
            return response.data
        } catch (error) {
            throw new Error(error.response?.data?.error || error.message)
        }
    },
    // 分析代码
    async SASTanalyze(extractPath, filePath, analysisType) {
        try {
            const response = await fetch(`${API_BASE_URL}/api/SASTanalyze`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    extractPath,
                    filePath,
                    analysisType
                })
            });
            
            const data = await response.json();
            return data;
        } catch (error) {
            console.error('API call failed:', error);
            return {
                success: false,
                error: error.message,
                data: { vulnerabilities: [] }
            };
        }
    },
    // 获取文件内容
    async getFileContent(filePath) {
        const response = await axios.get(`${API_BASE_URL}/file-content`, {
            params: { path: filePath }
        })

        return response.data
    },

    // 清理临时文件
    async cleanup(extractPath) {
        const response = await axios.post(`${API_BASE_URL}/cleanup`, {
            extractPath
        })

        return response.data
    }
    
}

export default api
