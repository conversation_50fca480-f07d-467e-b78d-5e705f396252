<template>
    <div class="repository-page">
        <el-row :gutter="20">
            <el-col :span="24">
                <el-card class="main-card">
                    <div slot="header" class="card-header">
                        <span class="header-title">
                            <i class="el-icon-folder"></i>
                            代码仓库管理
                        </span>
                        <div class="header-actions">
                            <el-button 
                                type="primary" 
                                size="small" 
                                icon="el-icon-refresh"
                                @click="loadRepositories"
                                :loading="loading"
                            >
                                刷新列表
                            </el-button>
                        </div>
                    </div>

                    <!-- 文件上传组件 -->
                    <file-upload 
                        @upload-success="handleUploadSuccess"
                        @repository-selected="handleRepositorySelected"
                        @repository-initialized="handleRepositoryInitialized"
                        ref="fileUpload"
                    />

                    <!-- 当前选中的仓库信息 -->
                    <div v-if="currentRepository" class="current-repo-info">
                        <el-alert
                            :title="`当前选中仓库: ${currentRepository.name}`"
                            type="success"
                            show-icon
                            :closable="false"
                        >
                            <div class="repo-details">
                                <span><i class="el-icon-document"></i> {{ currentRepository.fileCount }} 个文件</span>
                                <span><i class="el-icon-files"></i> {{ formatSize(currentRepository.size) }}</span>
                                <span><i class="el-icon-folder-opened"></i> {{ currentRepository.extractPath }}</span>
                            </div>
                        </el-alert>
                    </div>

                    <!-- 仓库列表 -->
                    <div class="repository-list" v-if="repositories.length > 0">
                        <h3 class="section-title">
                            <i class="el-icon-files"></i>
                            所有仓库
                        </h3>
                        
                        <el-table
                            :data="repositories"
                            style="width: 100%"
                            :row-class-name="tableRowClassName"
                            @row-click="handleRowClick"
                        >
                            <el-table-column prop="name" label="仓库名称" min-width="200">
                                <template slot-scope="scope">
                                    <div class="repo-name-cell">
                                        <i class="el-icon-folder"></i>
                                        <span>{{ scope.row.name }}</span>
                                        <el-tag 
                                            v-if="isCurrentRepo(scope.row)"
                                            type="success"
                                            size="mini"
                                        >
                                            当前选中
                                        </el-tag>
                                    </div>
                                </template>
                            </el-table-column>
                            
                            <el-table-column prop="uploadTime" label="创建时间" width="180">
                                <template slot-scope="scope">
                                    {{ formatDate(scope.row.uploadTime) }}
                                </template>
                            </el-table-column>
                            
                            <el-table-column prop="fileCount" label="文件数量" width="120" align="center">
                                <template slot-scope="scope">
                                    <el-badge :value="scope.row.fileCount" class="item" type="primary"/>
                                </template>
                            </el-table-column>
                            
                            <el-table-column prop="size" label="大小" width="120">
                                <template slot-scope="scope">
                                    {{ formatSize(scope.row.size) }}
                                </template>
                            </el-table-column>
                            
                            <el-table-column label="状态" width="120" align="center">
                                <template slot-scope="scope">
                                    <el-tag 
                                        :type="scope.row.initialized ? 'success' : 'info'"
                                        size="small"
                                    >
                                        {{ scope.row.initialized ? '已初始化' : '未初始化' }}
                                    </el-tag>
                                </template>
                            </el-table-column>
                            
                            <el-table-column label="操作" fixed="right" width="300">
                                <template slot-scope="scope">
                                    <el-button
                                        type="primary"
                                        size="mini"
                                        @click.stop="selectRepository(scope.row)"
                                        :disabled="isCurrentRepo(scope.row)"
                                    >
                                        {{ isCurrentRepo(scope.row) ? '已选择' : '选择' }}
                                    </el-button>
                                    <el-button
                                        type="success"
                                        size="mini"
                                        @click.stop="viewRepository(scope.row)"
                                        icon="el-icon-view"
                                    >
                                        查看文件
                                    </el-button>
                                    <el-button
                                        type="warning"
                                        size="mini"
                                        @click.stop="initializeRepository(scope.row)"
                                        :loading="scope.row.initializing"
                                        :disabled="scope.row.initialized"
                                    >
                                        {{ scope.row.initialized ? '已初始化' : '初始化' }}
                                    </el-button>
                                    <el-button
                                        type="danger"
                                        size="mini"
                                        style="margin-left: 1px;"
                                        @click.stop="deleteRepository(scope.row)"
                                        icon="el-icon-delete"
                                    >
                                        删除
                                    </el-button>
                                </template>
                            </el-table-column>
                        </el-table>
                    </div>

                    <!-- 空状态 -->
                    <div v-else-if="!loading" class="empty-state">
                        <i class="el-icon-folder-opened"></i>
                        <p>暂无仓库，请上传代码压缩包</p>
                    </div>
                </el-card>
            </el-col>
        </el-row>

        <!-- 文件浏览器对话框 -->
        <el-dialog
            title="仓库文件浏览器"
            :visible.sync="fileExplorerVisible"
            width="85%"
            top="5vh"
            :close-on-click-modal="false"
        >
            <file-explorer
                v-if="explorerRepository"
                :repository="explorerRepository"
                @file-selected="handleFileSelected"
            />
        </el-dialog>
    </div>
</template>

<script>
import FileUpload from '@/components/FileUpload.vue'
import FileExplorer from '@/components/FileExplorer.vue'
import api from '@/api'
import { mapState, mapActions } from 'vuex'

export default {
    name: 'Repository',
    components: {
        FileUpload,
        FileExplorer
    },
    data() {
        return {
            fileExplorerVisible: false,
            explorerRepository: null,
            loading: false,
            repositories: [],
            currentRepository: null
        }
    },
    
    mounted() {
        // 页面加载时自动获取仓库列表
        this.loadRepositories()
        
        // 从 localStorage 恢复上次选择
        this.restoreLastSelection()
    },
    
    methods: {
        ...mapActions(['uploadRepository']),

        // 加载仓库列表
        async loadRepositories() {
            this.loading = true
            try {
                const response = await api.getRepositories()
                
                if (response.success) {
                    this.repositories = response.data.map(repo => ({
                        ...repo,
                        initialized: this.checkInitialized(repo),
                        initializing: false
                    }))
                    
                    // 如果有保存的选择，自动选中
                    const lastSelected = localStorage.getItem('lastSelectedRepository')
                    if (lastSelected && !this.currentRepository) {
                        const repo = this.repositories.find(r => r.name === lastSelected)
                        if (repo) {
                             this.selectRepository(repo)
                        }
                    }
                } else {
                    this.$message.error('加载仓库列表失败')
                }
            } catch (error) {
                console.error('加载仓库列表失败:', error)
                this.$message.error('加载仓库列表失败: ' + error.message)
            } finally {
                this.loading = false
            }
        },

        // 检查仓库是否已初始化（可以通过检查是否有某些标记文件或数据库记录）
        checkInitialized(repo) {
           
            return true
        },

        // 恢复上次选择
        restoreLastSelection() {
            const lastSelected = localStorage.getItem('lastSelectedRepository')
            if (lastSelected) {
                // 通知 FileUpload 组件
                this.$nextTick(() => {
                    if (this.$refs.fileUpload) {
                        this.$refs.fileUpload.loadExistingRepositories()
                    }
                })
            }
        },

        // 选择仓库
        selectRepository(repository) {
            this.currentRepository = repository
            localStorage.setItem('lastSelectedRepository', repository.name)
            this.$store.commit('SET_CURRENT_REPOSITORY', repository)
            
            // 通知 FileUpload 组件
            if (this.$refs.fileUpload) {
                this.$refs.fileUpload.currentExtractPath = repository.extractPath
            }
            
            this.$message.success(`已选择仓库: ${repository.name}`)
        },

        // 处理上传成功
        handleUploadSuccess(repository) {
            this.uploadRepository(repository)
            this.currentRepository = repository
            this.$message.success('仓库上传成功')
            
            // 刷新列表
            this.loadRepositories()
        },

        // 处理仓库选择
        handleRepositorySelected(repository) {
            this.currentRepository = repository
            this.$store.commit('SET_CURRENT_REPOSITORY', repository)
        },

        // 处理仓库初始化完成
        handleRepositoryInitialized(data) {
            if (this.currentRepository) {
                this.currentRepository.initialized = true
                // 刷新列表以更新状态
                this.loadRepositories()
            }
        },

        // 初始化仓库
        async initializeRepository(repository) {
            this.$set(repository, 'initializing', true)
            
            try {
                const response = await api.analyzeRepository(repository.extractPath)
                
                if (response.success) {
                    this.$message.success('仓库初始化成功')
                    this.$set(repository, 'initialized', true)
                    
                    // 如果是当前仓库，更新状态
                    if (this.isCurrentRepo(repository)) {
                        this.currentRepository.initialized = true
                    }
                } else {
                    throw new Error(response.error || '初始化失败')
                }
            } catch (error) {
                this.$message.error('仓库初始化失败: ' + error.message)
            } finally {
                this.$set(repository, 'initializing', false)
            }
        },

        // 查看仓库文件
        viewRepository(repository) {
            this.explorerRepository = repository
            this.fileExplorerVisible = true
        },

        // 删除仓库
        async deleteRepository(repository) {
            try {
                await this.$confirm(
                    `确定要删除仓库 "${repository.name}" 吗？此操作将删除所有相关文件。`,
                    '删除确认',
                    {
                        confirmButtonText: '确定删除',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }
                )
                
                const response = await api.deleteRepository(repository.name)
                
                if (response.success) {
                    this.$message.success('删除成功')
                    
                    // 如果删除的是当前仓库，清空选择
                    if (this.isCurrentRepo(repository)) {
                        this.currentRepository = null
                        localStorage.removeItem('lastSelectedRepository')
                        this.$store.commit('SET_CURRENT_REPOSITORY', null)
                    }
                    
                    // 刷新列表
                    this.loadRepositories()
                } else {
                    throw new Error(response.error || '删除失败')
                }
            } catch (error) {
                if (error !== 'cancel') {
                    this.$message.error('删除失败: ' + error.message)
                }
            }
        },

        // 处理文件选择
        handleFileSelected(file) {
            this.$store.dispatch('selectFile', file)
            this.$router.push({ 
                name: 'analysis',
                params: { 
                    repository: this.explorerRepository.name,
                    filePath: file.path 
                }
            })
        },

        // 处理行点击
        handleRowClick(row) {
            this.selectRepository(row)
        },

        // 表格行样式
        tableRowClassName({ row }) {
            return this.isCurrentRepo(row) ? 'current-row' : ''
        },

        // 判断是否为当前仓库
        isCurrentRepo(repo) {
            return this.currentRepository && 
                   this.currentRepository.extractPath === repo.extractPath
        },

        // 格式化日期
        formatDate(date) {
            return new Date(date).toLocaleString('zh-CN')
        },

        // 格式化文件大小
        formatSize(bytes) {
            if (bytes < 1024) return bytes + ' B'
            else if (bytes < 1048576) return Math.round(bytes / 1024) + ' KB'
            else if (bytes < 1073741824) return Math.round(bytes / 1048576) + ' MB'
            else return Math.round(bytes / 1073741824) + ' GB'
        }
    }
}
</script>

<style scoped>
::v-deep .el-button + .el-button {
    margin-left: 1px !important;  /* 使用 !important 确保覆盖默认样式 */
}
.repository-page {
    padding: 20px;
    background-color: #f0f2f5;
    min-height: calc(100vh - 60px);
}

.main-card {
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header-title {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 18px;
    font-weight: 500;
}

.header-actions {
    display: flex;
    gap: 10px;
}

.current-repo-info {
    margin: 20px 0;
}

.repo-details {
    display: flex;
    gap: 20px;
    margin-top: 10px;
}

.repo-details span {
    display: flex;
    align-items: center;
    gap: 5px;
    color: #606266;
}

.repository-list {
    margin-top: 30px;
}

.section-title {
    display: flex;
    align-items: center;
    gap: 8px;
    margin: 0 0 20px 0;
    font-size: 16px;
    color: #303133;
}

.repo-name-cell {
    display: flex;
    align-items: center;
    gap: 8px;
}

.empty-state {
    text-align: center;
    padding: 60px 0;
    color: #909399;
}

.empty-state i {
    font-size: 48px;
    margin-bottom: 16px;
    display: block;
}

.empty-state p {
    font-size: 14px;
}

/* 表格样式 */
::v-deep .el-table {
    border-radius: 8px;
    overflow: hidden;
}

::v-deep .el-table th {
    background-color: #f5f7fa;
    color: #303133;
    font-weight: 500;
}

::v-deep .el-table .current-row td {
    background-color: #ecf5ff;
}

::v-deep .el-table tbody tr {
    cursor: pointer;
    transition: all 0.3s ease;
}

::v-deep .el-table tbody tr:hover {
    background-color: #f5f7fa;
}

/* 徽章样式 */
::v-deep .el-badge__content {
    background-color: #409eff;
}

/* 按钮组样式 */
.el-button--mini {
    padding: 7px 10px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    ::v-deep .el-table {
        font-size: 12px;
    }
    
    .el-button--mini {
        padding: 5px 8px;
        font-size: 12px;
    }
}
</style>
