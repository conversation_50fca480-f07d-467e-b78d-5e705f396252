<template>
    <div class="home-page">
        <el-row :gutter="20">
            <el-col :span="24">
                <div class="welcome-section">
                    <h2>欢迎使用脆弱性检测平台</h2>
                    <p>本平台提供全面的代码安全分析功能，帮助您识别和修复潜在的安全漏洞</p>
                </div>
            </el-col>
        </el-row>

        <el-row :gutter="20" class="feature-section">
            <el-col :span="8">
                <el-card class="feature-card">
                    <i class="el-icon-upload"></i>
                    <h3>代码仓库上传</h3>
                    <p>支持多种格式的代码仓库上传，快速导入项目进行分析</p>
                    <el-button type="primary" @click="navigateTo('repository')">
                        开始上传
                    </el-button>
                </el-card>
            </el-col>

            <el-col :span="8">
                <el-card class="feature-card">
                    <i class="el-icon-search"></i>
                    <h3>漏洞检测分析</h3>
                    <p>综合多种技术手段，深度分析代码中的安全漏洞</p>
                    <el-button type="primary" @click="navigateTo('analysis')">
                        开始分析
                    </el-button>
                </el-card>
            </el-col>

            <el-col :span="8">
                <el-card class="feature-card">
                    <i class="el-icon-document"></i>
                    <h3>结果保存导出</h3>
                    <p>自动保存分析结果，支持多种格式导出分析报告</p>
                    <el-button type="primary" @click="showResults">
                        查看结果
                    </el-button>
                </el-card>
            </el-col>
        </el-row>

        <el-row :gutter="20" class="stats-section" v-if="statistics">
            <el-col :span="6">
                <el-card class="stat-card">
                    <div class="stat-value">{{ statistics.repositories }}</div>
                    <div class="stat-label">已上传仓库</div>
                </el-card>
            </el-col>
            <el-col :span="6">
                <el-card class="stat-card">
                    <div class="stat-value">{{ statistics.filesAnalyzed }}</div>
                    <div class="stat-label">已分析文件</div>
                </el-card>
            </el-col>
            <el-col :span="6">
                <el-card class="stat-card">
                    <div class="stat-value">{{ statistics.vulnerabilities }}</div>
                    <div class="stat-label">发现漏洞</div>
                </el-card>
            </el-col>
            <el-col :span="6">
                <el-card class="stat-card">
                    <div class="stat-value">{{ statistics.fixedIssues }}</div>
                    <div class="stat-label">已修复问题</div>
                </el-card>
            </el-col>
        </el-row>
    </div>
</template>

<script>
export default {
    name: 'Home',
    data() {
        return {
            statistics: {
                repositories: 12,
                filesAnalyzed: 156,
                vulnerabilities: 43,
                fixedIssues: 28
            }
        }
    },
    methods: {
        navigateTo(page) {
            this.$router.push({ name: page })
        },
        showResults() {
            this.$message.info('请先进行代码分析')
        }
    }
}
</script>

<style scoped>
.home-page {
    padding: 20px;
}

.welcome-section {
    text-align: center;
    margin-bottom: 40px;
}

.welcome-section h2 {
    font-size: 32px;
    margin-bottom: 10px;
    color: #303133;
}

.welcome-section p {
    font-size: 16px;
    color: #606266;
}

.feature-section {
    margin-bottom: 40px;
}

.feature-card {
    text-align: center;
    padding: 30px 20px;
    transition: all 0.3s;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.feature-card i {
    font-size: 48px;
    color: #409EFF;
    margin-bottom: 20px;
}

.feature-card h3 {
    margin-bottom: 15px;
    color: #303133;
}

.feature-card p {
    color: #606266;
    margin-bottom: 20px;
    min-height: 48px;
}

.stats-section {
    margin-top: 40px;
}

.stat-card {
    text-align: center;
    padding: 20px;
}

.stat-value {
    font-size: 36px;
    font-weight: bold;
    color: #409EFF;
    margin-bottom: 10px;
}

.stat-label {
    font-size: 14px;
    color: #606266;
}
</style>
