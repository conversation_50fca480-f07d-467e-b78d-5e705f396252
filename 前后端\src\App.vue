<template>
    <div id="app">
        <el-container>
            <el-header>
                <div class="header-content">
                    <h1>脆弱性检测平台</h1>
                    <el-menu
                            :default-active="activeMenu"
                            class="header-menu"
                            mode="horizontal"
                            @select="handleSelect"
                    >
                        <el-menu-item index="home">首页</el-menu-item>
                        <el-menu-item index="repository">代码仓库</el-menu-item>
                        <el-menu-item index="analysis">漏洞分析</el-menu-item>
                        <el-menu-item index="VulnerabilityDashboard">
                        <i class="el-icon-data-analysis"></i>
                        <span slot="title">漏洞分析仪表板</span>
                    </el-menu-item>
                                        </el-menu>
                </div>
            </el-header>
            <el-main>
                <router-view />
            </el-main>
        </el-container>
    </div>
</template>

<script>
export default {
    name: 'App',
    computed: {
        activeMenu() {
            return this.$route.name || 'home'
        }
    },
    methods: {
        handleSelect(key) {
            this.$router.push({ name: key })
        }
    }
}
</script>

<style>
#app {
    font-family: Avenir, Helvetica, Arial, sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    color: #2c3e50;
}

.el-header {
    background-color: #409EFF;
    color: #fff;
    box-shadow: 0 2px 4px rgba(0,0,0,.12);
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 60px;
}

.header-content h1 {
    margin: 0;
    font-size: 24px;
}

.header-menu {
    background-color: transparent;
    border: none;
}

.header-menu .el-menu-item {
    color: #fff;
}

.header-menu .el-menu-item:hover {
    background-color: rgba(255,255,255,0.1);
}

.header-menu .el-menu-item.is-active {
    background-color: rgba(255,255,255,0.2);
    border-bottom: 2px solid #fff;
}
</style>
