# Jupyter Notebooks 简要说明

## 概述
本目录包含6个notebook文件，用于实验环境以及组件测试。

## 文件说明

### 1. envi.ipynb - 环境配置
**功能**: 安装依赖库和下载模型
- 安装transformers、faiss-cpu
- 配置网络代理
- 下载CodeBERT预训练模型

### 2. preprocess.ipynb - 数据预处理  
**功能**: 处理测试套件数据
- 解压ZIP文件
- 提取代码块
- 代码清理和CWE标签提取


### 3. codebert.ipynb - CodeBERT嵌入
**功能**: 生成代码向量嵌入
- 使用CodeBERT模型
- 批量处理代码片段
- 生成768维向量


### 4. graphcodebert-base.ipynb - GraphCodeBERT嵌入
**功能**: 生成图结构感知的代码嵌入
- 使用GraphCodeBERT模型
- 考虑代码图结构信息
- 与CodeBERT嵌入对比

### 5. VectorBD2.ipynb - FAISS索引构建
**功能**: 构建向量检索索引
- L2向量归一化
- 构建IndexFlatIP索引
- 支持余弦相似度检索

### 6. query2.ipynb - 端到端测试
**功能**: 完整流程验证
- 代码预处理测试
- 向量检索验证
- 静态分析集成
- 漏洞检测演示

## 执行顺序
1. envi.ipynb (环境配置)
2. preprocess.ipynb (数据预处理)
3. codebert.ipynb 或 graphcodebert-base.ipynb (嵌入生成)
4. VectorBD2.ipynb (索引构建)
5. query2.ipynb (测试验证)

## 数据流
```
测试套件 → 预处理数据 → 向量嵌入 → FAISS索引 → 检索测试
```


