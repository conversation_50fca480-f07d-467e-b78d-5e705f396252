import Vue from 'vue'
import Vuex from 'vuex'

Vue.use(Vuex)

export default new Vuex.Store({
  state: {
    repositories: [],
    currentRepository: null,
    selectedFile: null,
    analysisResults: [],
    analysisHistory: []
  },

  mutations: {
    ADD_REPOSITORY(state, repository) {
      state.repositories.push(repository)
    },

    SET_CURRENT_REPOSITORY(state, repository) {
      state.currentRepository = repository
    },

    SET_SELECTED_FILE(state, file) {
      state.selectedFile = file
    },

    ADD_ANALYSIS_RESULT(state, result) {
      state.analysisResults.push(result)
      state.analysisHistory.push({
        ...result,
        timestamp: new Date()
      })
    },

    CLEAR_ANALYSIS_RESULTS(state) {
      state.analysisResults = []
    }
  },

  actions: {
    uploadRepository({ commit }, repository) {
      commit('ADD_REPOSITORY', repository)
      commit('SET_CURRENT_REPOSITORY', repository)
    },

    selectFile({ commit }, file) {
      commit('SET_SELECTED_FILE', file)
    },

    saveAnalysisResult({ commit }, result) {
      commit('ADD_ANALYSIS_RESULT', result)
    }
  },

  getters: {
    getRepositoryById: (state) => (id) => {
      return state.repositories.find(repo => repo.id === id)
    },

    currentAnalysisResults: (state) => {
      return state.analysisResults
    },

    analysisHistoryByFile: (state) => (fileId) => {
      return state.analysisHistory.filter(result => result.fileId === fileId)
    }
  }
})
