<template>
    <div class="file-explorer">
        <!-- 文件树结构 -->
        <el-tree
            :data="repository.files"
            :props="treeProps"
            @node-click="handleNodeClick"
            default-expand-all
            highlight-current
        >
            <span class="custom-tree-node" slot-scope="{ node, data }">
                <i :class="getFileIcon(data)"></i>
                <span>{{ data.name }}</span>
                <span class="file-size" v-if="data.type === 'file'">
                    ({{ formatSize(data.size) }})
                </span>
            </span>
        </el-tree>

        <!-- 文件预览区域 -->
        <el-dialog
            title="文件内容"
            :visible.sync="previewVisible"
            width="80%"
            top="10vh"
        >
            <pre v-if="fileContent" class="file-content">{{ fileContent }}</pre>
            <div v-else class="loading">
                <i class="el-icon-loading"></i> 加载中...
            </div>
        </el-dialog>
    </div>
</template>

<script>
import J<PERSON><PERSON><PERSON> from 'jszip'

export default {
    name: 'FileExplorer',
    props: {
        repository: {
            type: Object,
            required: true
        }
    },
    data() {
        return {
            treeProps: {
                children: 'children',
                label: 'name'
            },
            previewVisible: false,
            fileContent: null
        }
    },
    methods: {
        async handleNodeClick(data) {
            if (data.type === 'file') {
                await this.loadFileContent(data)
                this.$emit('file-selected', data)
            }
        },

        async loadFileContent(file) {
            this.previewVisible = true
            this.fileContent = null

            try {
                if (this.repository.fileType === 'zip' && file.zipEntry) {
                    // 从 ZIP 文件中读取内容
                    const content = await file.zipEntry.async('string')
                    this.fileContent = content
                } else if (this.repository.fileType === 'tar.gz' && file.tarEntry) {
                    // 从 tar.gz 文件中读取内容
                    const decoder = new TextDecoder('utf-8')
                    this.fileContent = decoder.decode(file.tarEntry.data)
                }
            } catch (error) {
                this.$message.error('读取文件内容失败：' + error.message)
                this.fileContent = '文件内容读取失败'
            }
        },

        getFileIcon(data) {
            if (data.type === 'folder') {
                return 'el-icon-folder'
            }
            
            const ext = data.name.split('.').pop().toLowerCase()
            const iconMap = {
                'js': 'el-icon-document',
                'vue': 'el-icon-document',
                'py': 'el-icon-document',
                'java': 'el-icon-document',
                'c': 'el-icon-document',
                'cpp': 'el-icon-document',
                'h': 'el-icon-document',
                'json': 'el-icon-document',
                'xml': 'el-icon-document',
                'md': 'el-icon-document-copy',
                'txt': 'el-icon-document-copy',
                'png': 'el-icon-picture',
                'jpg': 'el-icon-picture',
                'jpeg': 'el-icon-picture',
                'gif': 'el-icon-picture'
            }
            
            return iconMap[ext] || 'el-icon-document'
        },

        formatSize(bytes) {
            if (!bytes) return '0 B'
            if (bytes < 1024) return bytes + ' B'
            else if (bytes < 1048576) return Math.round(bytes / 1024) + ' KB'
            else if (bytes < 1073741824) return Math.round(bytes / 1048576) + ' MB'
            else return Math.round(bytes / 1073741824) + ' GB'
        }
    }
}
</script>

<style scoped>
.file-explorer {
    height: 60vh;
    overflow-y: auto;
}

.custom-tree-node {
    display: flex;
    align-items: center;
    font-size: 14px;
}

.custom-tree-node i {
    margin-right: 8px;
    color: #409EFF;
}

.file-size {
    margin-left: 8px;
    color: #909399;
    font-size: 12px;
}

.file-content {
    max-height: 60vh;
    overflow-y: auto;
    background-color: #f5f7fa;
    padding: 15px;
    border-radius: 4px;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 14px;
    line-height: 1.5;
}

.loading {
    text-align: center;
    padding: 40px;
    color: #909399;
}
</style>
