#include <stdio.h>
#include "math_utils.h"
#include "config.h"

int main() {
    // 使用 config.h 中的全局变量
    printf("Program: %s\n", program_name);
    printf("Version: %d.%d\n", VERSION_MAJOR, VERSION_MINOR);
    
    // 使用 math_utils.h 中的函数
    int result = add(10, 20);
    printf("10 + 20 = %d\n", result);
    
    result = multiply(5, 6);
    printf("5 * 6 = %d\n", result);
    
    // 使用 config.h 中的全局变量
    if (debug_mode) {
        printf("Debug mode is ON\n");
    }
    
    // 使用未定义的外部函数
    print_stats();
    
    return 0;
}
