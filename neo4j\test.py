#!/usr/bin/env python3

import sys
import os

# 确保不会导入本地的 neo4j 模块
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from neo4j import GraphDatabase
    print("✓ neo4j 包导入成功")
except ImportError as e:
    print(f"✗ 导入失败: {e}")
    print("请确保：")
    print("1. 已安装 neo4j: pip install neo4j")
    print("2. 当前文件不是命名为 neo4j.py")
    print("3. 当前目录下没有 neo4j.py 文件")
    sys.exit(1)


def test_connection():
    """测试 Neo4j 连接"""
    # Neo4j 连接参数
    uri = "bolt://localhost:7687"
    username = "neo4j"
    password = "password"  # 请修改为你的实际密码
    
    print(f"\n尝试连接到 Neo4j...")
    print(f"URI: {uri}")
    print(f"用户名: {username}")
    
    try:
        # 创建驱动实例
        driver = GraphDatabase.driver(uri, auth=(username, password))
        
        # 验证连接
        driver.verify_connectivity()
        print("✓ 连接成功！")
        
        # 执行简单查询
        with driver.session() as session:
            result = session.run("RETURN 'Hello from Neo4j!' as message")
            record = result.single()
            print(f"✓ 查询成功: {record['message']}")
        
        # 关闭连接
        driver.close()
        print("✓ 连接已正常关闭")
        
    except Exception as e:
        print(f"✗ 连接失败: {type(e).__name__}: {e}")
        print("\n可能的解决方案：")
        print("1. 检查 Neo4j 服务是否运行: sudo systemctl status neo4j")
        print("2. 检查连接地址和端口是否正确")
        print("3. 检查用户名和密码是否正确")
        print("4. 检查防火墙设置")


if __name__ == "__main__":
    test_connection()
