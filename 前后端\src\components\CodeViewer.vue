<template>
    <div class="code-viewer">
        <div class="code-header">
            <span class="file-path">{{ file.path || file.name }}</span>
            <el-button-group>
                <el-button size="mini" icon="el-icon-copy-document" @click="copyCode">
                    复制
                </el-button>
                <el-button size="mini" icon="el-icon-download" @click="downloadCode">
                    下载
                </el-button>
                <el-button 
                    size="mini" 
                    icon="el-icon-full-screen" 
                    @click="toggleFullscreen"
                    :title="isFullscreen ? '退出全屏' : '全屏'"
                >
                </el-button>
            </el-button-group>
        </div>

<div class="code-container" ref="codeContainer" @mouseup="handleTextSelection">
            <div class="code-wrapper" ref="codeWrapper">
                <div class="line-numbers" ref="lineNumbers">
                    <div 
                        v-for="line in lineCount" 
                        :key="line"
                        :ref="`line-${line}`"
                        class="line-number"
                        :class="{ 
                            'highlighted-line': isLineHighlighted(line),
                            'vulnerability-line': hasVulnerability(line)
                        }"
                        @click="toggleLineSelection(line)"
                    >
                        {{ line }}
                    </div>
                </div>
                <pre class="code-content" ref="codeContent"><code :class="`language-${language}`" v-html="highlightedCode"></code></pre>
            </div>

            <!-- 漏洞标记 - 修改位置计算 -->
            <div
                v-for="highlight in processedHighlights"
                :key="`${highlight.id || highlight.startLine}`"
                class="vulnerability-marker"
                :class="[`severity-${highlight.severity}`, { active: highlight.active }]"
                :style="getMarkerStyle(highlight)"
                @click="showVulnerabilityDetail(highlight)"
            >
                <el-tooltip :content="highlight.message" placement="left">
                    <i :class="getVulnerabilityIcon(highlight.severity)"></i>
                </el-tooltip>
            </div>
        </div>

        <!-- 代码选择信息 -->
        <div v-if="selectedLines.length > 0" class="selection-info">
            <span>已选择 {{ selectedLines.length }} 行</span>
            <el-button size="mini" @click="analyzeSelectedCode">分析选中代码</el-button>
            <el-button size="mini" @click="clearSelection">清除选择</el-button>
        </div>

        <!-- 搜索功能 -->
        <el-dialog
            title="搜索"
            :visible.sync="searchVisible"
            width="400px"
            :append-to-body="true"
        >
            <el-input
                v-model="searchText"
                placeholder="输入要搜索的内容"
                @keyup.enter="searchNext"
                ref="searchInput"
            >
                <el-button slot="append" @click="searchNext">下一个</el-button>
            </el-input>
            <div class="search-info" v-if="searchResults.length > 0">
                找到 {{ searchResults.length }} 个结果，当前第 {{ currentSearchIndex + 1 }} 个
            </div>
        </el-dialog>
    </div>
</template>

<script>
import hljs from 'highlight.js'
import 'highlight.js/styles/vs.css'

export default {
    name: 'CodeViewer',
    props: {
        file: {
            type: Object,
            required: true
        },
        content: {
            type: String,
            required: true
        },
        highlights: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            language: 'plaintext',
            selectedLines: [],
            selectedText: '',
            isFullscreen: false,
            searchVisible: false,
            searchText: '',
            searchResults: [],
            currentSearchIndex: 0,
            actualLineHeight: 22, // 实际行高，将动态计算
            codeContentPaddingTop: 0, // 代码区域的上边距
            lineNumbersWidth: 0 // 行号区域的宽度
        }
    },
    computed: {
        lineCount() {
            return this.content ? this.content.split('\n').length : 0
        },
        highlightedCode() {
            if (!this.content) return ''

            try {
                const result = hljs.highlight(this.content, { language: this.language })
                return this.addLineWrapping(result.value)
            } catch (e) {
                console.error('Highlight error:', e)
                return this.addLineWrapping(this.escapeHtml(this.content))
            }
        },
        // 处理高亮显示，包括解析行号信息
        processedHighlights() {
            return this.highlights.map(highlight => {
                let startLine = highlight.startLine || 0
                let endLine = highlight.endLine || 0

                // 如果是新格式的漏洞数据，尝试解析行号
                if (highlight.lineInfo) {
                    const lineNumbers = this.parseLineNumbers(highlight.lineInfo)
                    if (lineNumbers.length > 0) {
                        startLine = Math.min(...lineNumbers)
                        endLine = Math.max(...lineNumbers)
                    }
                }

                return {
                    ...highlight,
                    startLine,
                    endLine
                }
            })
        }
    },
    watch: {
        processedHighlights: {
            handler() {
                this.$nextTick(() => {
                    this.calculateDimensions()
                })
            },
            deep: true
        },
        
        // 监听内容变化
        content() {
            this.$nextTick(() => {
                this.calculateDimensions()
            })
        },
        file: {
            immediate: true,
            handler(newFile) {
                if (newFile) {
                    this.language = this.detectLanguage(newFile.name)
                }
            }
        },
        searchText(val) {
            if (val) {
                this.performSearch()
            } else {
                this.clearSearch()
            }
        }
    },
    mounted() {
        document.addEventListener('keydown', this.handleKeyboard)
             this.$nextTick(() => {
            this.calculateDimensions()
            // 监听窗口大小变化，重新计算
            window.addEventListener('resize', this.calculateDimensions)
        })
    },
    beforeDestroy() {
        document.removeEventListener('keydown', this.handleKeyboard)
        window.removeEventListener('resize', this.calculateDimensions)
    },
    methods: {
        calculateDimensions() {
            // 计算实际行高
            const lineNumbers = this.$refs.lineNumbers
            if (lineNumbers && lineNumbers.children.length > 1) {
                const firstLine = lineNumbers.children[0]
                const secondLine = lineNumbers.children[1]
                const firstRect = firstLine.getBoundingClientRect()
                const secondRect = secondLine.getBoundingClientRect()
                this.actualLineHeight = secondRect.top - firstRect.top
            }

            // 获取代码内容区域的 padding
            const codeContent = this.$refs.codeContent
            if (codeContent) {
                const computedStyle = window.getComputedStyle(codeContent)
                this.codeContentPaddingTop = parseInt(computedStyle.paddingTop)
            }

            // 获取行号区域的宽度
            if (lineNumbers) {
                this.lineNumbersWidth = lineNumbers.offsetWidth
            }
        },
        detectLanguage(filename) {
            const ext = filename.split('.').pop().toLowerCase()
            const langMap = {
                'js': 'javascript',
                'jsx': 'javascript',
                'ts': 'typescript',
                'tsx': 'typescript',
                'c': 'c',
                'cpp': 'cpp',
                'cc': 'cpp',
                'cxx': 'cpp',
                'h': 'c',
                'hpp': 'cpp',
                'java': 'java',
                'py': 'python',
                'rb': 'ruby',
                'go': 'go',
                'rs': 'rust',
                'php': 'php',
                'cs': 'csharp',
                'swift': 'swift',
                'kt': 'kotlin',
                'scala': 'scala',
                'r': 'r',
                'sql': 'sql',
                'sh': 'bash',
                'bash': 'bash',
                'yml': 'yaml',
                'yaml': 'yaml',
                'json': 'json',
                'xml': 'xml',
                'html': 'html',
                'htm': 'html',
                'css': 'css',
                'scss': 'scss',
                'sass': 'sass',
                'less': 'less',
                'md': 'markdown',
                'vue': 'vue'
            }
            return langMap[ext] || 'plaintext'
        },

        // 解析行号信息（如 "9行", "第10行", "9-11行" 等）
        parseLineNumbers(lineInfo) {
            if (!lineInfo) return []
            
            const lineNumbers = []
            // 匹配各种行号格式
            const patterns = [
                /(\d+)行/g,
                /第(\d+)行/g,
                /line\s*(\d+)/ig,
                /(\d+)-(\d+)行/g,
                /第(\d+)-(\d+)行/g
            ]

            for (const pattern of patterns) {
                let match
                while ((match = pattern.exec(lineInfo)) !== null) {
                    if (match[2]) {
                        // 范围格式 (如 "9-11行")
                        const start = parseInt(match[1])
                        const end = parseInt(match[2])
                        for (let i = start; i <= end; i++) {
                            lineNumbers.push(i)
                        }
                    } else {
                        // 单行格式
                        lineNumbers.push(parseInt(match[1]))
                    }
                }
            }

            return [...new Set(lineNumbers)].sort((a, b) => a - b)
        },

        addLineWrapping(html) {
            const lines = html.split('\n')
            return lines.map((line, index) => {
                const lineNum = index + 1
                const isHighlighted = this.processedHighlights.some(h => 
                    lineNum >= h.startLine && lineNum <= h.endLine
                )
                return `<span class="code-line ${isHighlighted ? 'has-vulnerability' : ''}" data-line="${lineNum}">${line || ' '}</span>`
            }).join('\n')
        },

        escapeHtml(text) {
            const div = document.createElement('div')
            div.textContent = text
            return div.innerHTML
        },

        isLineHighlighted(lineNum) {
            return this.selectedLines.includes(lineNum) || 
                   this.processedHighlights.some(h => lineNum >= h.startLine && lineNum <= h.endLine)
        },

        hasVulnerability(lineNum) {
            return this.processedHighlights.some(h => 
                lineNum >= h.startLine && lineNum <= h.endLine
            )
        },

        toggleLineSelection(lineNum) {
            const index = this.selectedLines.indexOf(lineNum)
            if (index > -1) {
                this.selectedLines.splice(index, 1)
            } else {
                this.selectedLines.push(lineNum)
                this.selectedLines.sort((a, b) => a - b)
            }
        },

        clearSelection() {
            this.selectedLines = []
            this.selectedText = ''
        },

        handleTextSelection() {
            const selection = window.getSelection()
            if (selection.toString().trim()) {
                this.selectedText = selection.toString()
                
                const range = selection.getRangeAt(0)
                const startContainer = range.startContainer
                const endContainer = range.endContainer
                
                const startLine = this.findLineNumber(startContainer)
                const endLine = this.findLineNumber(endContainer)
                
                if (startLine && endLine) {
                    this.selectedLines = []
                    for (let i = startLine; i <= endLine; i++) {
                        this.selectedLines.push(i)
                    }
                }
            }
        },

        findLineNumber(node) {
            let current = node
            while (current && current !== this.$refs.codeContent) {
                if (current.classList && current.classList.contains('code-line')) {
                    return parseInt(current.getAttribute('data-line'))
                }
                current = current.parentElement
            }
            return null
        },
   
        getMarkerStyle(highlight) {
            // 获取第一行的实际位置作为基准
            const firstLineRef = this.$refs[`line-1`]
            if (!firstLineRef || !firstLineRef[0]) {
                // 如果还没有渲染，使用默认计算
                const top = (highlight.startLine - 1) * this.actualLineHeight + this.codeContentPaddingTop
                const height = (highlight.endLine - highlight.startLine + 1) * this.actualLineHeight
                return {
                    top: `${top}px`,
                    height: `${height}px`,
                    left: `${this.lineNumbersWidth}px`
                }
            }

            // 获取起始行和结束行的元素
            const startLineRef = this.$refs[`line-${highlight.startLine}`]
            const endLineRef = this.$refs[`line-${highlight.endLine}`]

            if (startLineRef && startLineRef[0] && endLineRef && endLineRef[0]) {
                const containerRect = this.$refs.codeWrapper.getBoundingClientRect()
                const startRect = startLineRef[0].getBoundingClientRect()
                const endRect = endLineRef[0].getBoundingClientRect()
                
                // 计算相对于容器的位置
                const top = startRect.top - containerRect.top
                const height = endRect.bottom - startRect.top

                return {
                    top: `${top}px`,
                    height: `${height}px`,
                    left: `${this.lineNumbersWidth}px`
                }
            }

            // 如果无法获取元素，使用计算值
            const top = (highlight.startLine - 1) * this.actualLineHeight + this.codeContentPaddingTop
            const height = (highlight.endLine - highlight.startLine + 1) * this.actualLineHeight

            return {
                top: `${top}px`,
                height: `${height}px`,
                left: `${this.lineNumbersWidth}px`
            }
        },
        analyzeSelectedCode() {
            if (this.selectedLines.length > 0) {
                const lines = this.content.split('\n')
                const selectedCode = lines
                    .filter((_, index) => this.selectedLines.includes(index + 1))
                    .join('\n')
                
                this.$emit('code-selected', {
                    code: selectedCode,
                    startLine: Math.min(...this.selectedLines),
                    endLine: Math.max(...this.selectedLines)
                })
            }
        },

        

        getVulnerabilityIcon(severity) {
            const iconMap = {
                critical: 'el-icon-error',
                high: 'el-icon-warning',
                medium: 'el-icon-warning-outline',
                low: 'el-icon-info',
                info: 'el-icon-info'
            }
            return iconMap[severity] || 'el-icon-info'
        },

        showVulnerabilityDetail(highlight) {
            this.$emit('vulnerability-clicked', highlight)
            
            // 构建详情消息
            const messageElements = [
                this.$createElement('p', null, highlight.message),
                this.$createElement('p', { style: 'margin-top: 10px; color: #909399;' }, 
                    `行 ${highlight.startLine}-${highlight.endLine}`)
            ]

            // 如果有 CWE 信息，添加显示
            if (highlight.cwe) {
                messageElements.push(
                    this.$createElement('p', { style: 'margin-top: 10px; font-weight: bold;' }, 
                        `CWE-${highlight.cwe}`)
                )
            }

            // 如果有推理过程，添加显示
            if (highlight.reasoning) {
                messageElements.push(
                    this.$createElement('p', { style: 'margin-top: 10px; font-style: italic;' }, 
                        `分析: ${highlight.reasoning}`)
                )
            }

            if (highlight.suggestion) {
                messageElements.push(
                    this.$createElement('p', { style: 'margin-top: 10px; color: #67c23a;' }, 
                        `建议: ${highlight.suggestion}`)
                )
            }

            this.$notify({
                title: `${highlight.type} (${highlight.severity.toUpperCase()})`,
                message: this.$createElement('div', null, messageElements),
                type: this.getNotificationType(highlight.severity),
                duration: 0,
                position: 'bottom-right'
            })
        },

        getNotificationType(severity) {
            const typeMap = {
                critical: 'error',
                high: 'error',
                medium: 'warning',
                low: 'info',
                info: 'info'
            }
            return typeMap[severity] || 'info'
        },

        copyCode() {
            let textToCopy = this.content
            
            if (this.selectedLines.length > 0) {
                const lines = this.content.split('\n')
                textToCopy = lines
                    .filter((_, index) => this.selectedLines.includes(index + 1))
                    .join('\n')
            }
            
            navigator.clipboard.writeText(textToCopy).then(() => {
                this.$message.success(
                    this.selectedLines.length > 0 
                        ? '选中代码已复制到剪贴板' 
                        : '代码已复制到剪贴板'
                )
            }).catch(() => {
                const el = document.createElement('textarea')
                el.value = textToCopy
                document.body.appendChild(el)
                el.select()
                document.execCommand('copy')
                document.body.removeChild(el)
                this.$message.success('代码已复制到剪贴板')
            })
        },

        downloadCode() {
            const blob = new Blob([this.content], { type: 'text/plain' })
            const url = URL.createObjectURL(blob)
            const a = document.createElement('a')
            a.href = url
            a.download = this.file.name
            a.click()
            URL.revokeObjectURL(url)
        },

        toggleFullscreen() {
            this.isFullscreen = !this.isFullscreen
            this.$emit('fullscreen-change', this.isFullscreen)
            
            if (this.isFullscreen) {
                this.$el.classList.add('fullscreen')
            } else {
                this.$el.classList.remove('fullscreen')
            }
        },

        handleKeyboard(e) {
            if ((e.ctrlKey || e.metaKey) && e.key === 'f') {
                e.preventDefault()
                this.searchVisible = true
                this.$nextTick(() => {
                    this.$refs.searchInput?.focus()
                })
            }
        },

        performSearch() {
            if (!this.searchText) return
            
            this.searchResults = []
            const lines = this.content.split('\n')
            
            lines.forEach((line, index) => {
                if (line.toLowerCase().includes(this.searchText.toLowerCase())) {
                    this.searchResults.push({
                        lineNumber: index + 1,
                        content: line
                    })
                }
            })
            
            if (this.searchResults.length > 0) {
                this.currentSearchIndex = 0
                this.scrollToLine(this.searchResults[0].lineNumber)
            }
        },

        searchNext() {
            if (this.searchResults.length === 0) {
                this.performSearch()
                return
            }
            
            this.currentSearchIndex = (this.currentSearchIndex + 1) % this.searchResults.length
            this.scrollToLine(this.searchResults[this.currentSearchIndex].lineNumber)
        },

        clearSearch() {
            this.searchResults = []
            this.currentSearchIndex = 0
        },

       scrollToLine(lineNumber) {
            const lineRef = this.$refs[`line-${lineNumber}`]
            if (lineRef && lineRef[0]) {
                // 使用 scrollIntoView 获得更精确的滚动
                lineRef[0].scrollIntoView({
                    behavior: 'smooth',
                    block: 'center'
                })
            } else {
                // 备用方案
                const scrollTop = (lineNumber - 1) * this.actualLineHeight - 100
                this.$refs.codeContainer.scrollTo({
                    top: scrollTop,
                    behavior: 'smooth'
                })
            }
            
            // 临时高亮选中行
            this.selectedLines = [lineNumber]
            setTimeout(() => {
                if (this.searchVisible) {
                    this.selectedLines = []
                }
            }, 2000)
        },
        addLineWrapping(html) {
            const lines = html.split('\n')
            return lines.map((line, index) => {
                const lineNum = index + 1
                const isHighlighted = this.processedHighlights.some(h => 
                    lineNum >= h.startLine && lineNum <= h.endLine
                )
                // 确保每行都有明确的高度
                return `<span class="code-line ${isHighlighted ? 'has-vulnerability' : ''}" data-line="${lineNum}">${line || '&nbsp;'}</span>`
            }).join('\n')
        }
    }
}
</script>

<style scoped>
.code-viewer {
    height: 100%;
    display: flex;
    flex-direction: column;
    
}

.code-viewer.fullscreen {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 2000;
    background: white;
}

.code-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    background-color: #f5f7fa;
    border-bottom: 1px solid #e4e7ed;
}

.file-path {
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    color: #606266;
    font-size: 14px;
}

.code-container {
    flex: 1;
    overflow: auto;
    position: relative;
    background-color: #ffffff;
}

.code-wrapper {
    display: flex;
    min-height: 100%;
    position: relative; 
}

.line-numbers {
    background-color: #f5f7fa;
    padding: 10px 0;
    user-select: none;
    border-right: 1px solid #e4e7ed;
    text-align: right;
    position: sticky;
    left: 0;
    z-index: 1;
}

.line-number {
    padding: 0 15px;
    line-height: 1.5; /* 使用相对行高 */
    height: 22px; /* 明确指定高度 */
    color: #909399;
    cursor: pointer;
    transition: all 0.3s;
    box-sizing: border-box; /* 包含 padding 在内 */
}


.line-number:hover {
    background-color: #e6f7ff;
    color: #409eff;
}

.line-number.highlighted-line {
    background-color: #ecf5ff;
    color: #409eff;
    font-weight: bold;
}

.line-number.vulnerability-line {
    background-color: #fee;
    color: #f56c6c;
}

.code-content {
    flex: 1;
    margin: 0;
    padding: 10px 20px;
    background-color: transparent;
    overflow-x: auto;
}

.code-content code {
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 14px;
    line-height: 22px;
}

.code-line {
    display: block;
    height: 22px; /* 明确指定高度 */
    line-height: 22px; /* 与高度保持一致 */
    box-sizing: border-box;
}
.code-line:empty::before {
    content: '\00a0'; /* 不间断空格 */
}
.code-line.has-vulnerability {
    background-color: rgba(245, 108, 108, 0.05);
}

.vulnerability-marker {
    position: absolute;
    right: 0;
    background-color: rgba(245, 108, 108, 0.1);
    border-left: 3px solid;
    cursor: pointer;
    transition: all 0.3s;
    pointer-events: none;
    box-sizing: border-box; /* 包含边框 */
}
.vulnerability-marker i {
    position: absolute;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
    pointer-events: all;
    font-size: 16px;
}

.vulnerability-marker:hover {
    background-color: rgba(245, 108, 108, 0.2);
}

.vulnerability-marker.active {
    background-color: rgba(245, 108, 108, 0.3);
    animation: pulse 1.5s ease-in-out;
}

@keyframes pulse {
    0% {
        opacity: 0.6;
    }
    50% {
        opacity: 1;
    }
    100% {
        opacity: 0.6;
    }
}

.severity-critical {
    border-left-color: #dd0000;
}

.severity-high {
    border-left-color: #f56c6c;
}

.severity-medium {
    border-left-color: #e6a23c;
}

.severity-low {
    border-left-color: #f0d20e;
}

.severity-info {
    border-left-color: #909399;
}

.selection-info {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: #409eff;
    color: white;
    padding: 5px 15px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.selection-info .el-button {
    padding: 3px 10px;
}

.search-info {
    margin-top: 10px;
    color: #909399;
    font-size: 14px;
}

.code-content :deep(.hljs) {
    background: transparent;
}

.code-container::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

.code-container::-webkit-scrollbar-track {
    background: #f1f1f1;
}

.code-container::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

.code-container::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
</style>
