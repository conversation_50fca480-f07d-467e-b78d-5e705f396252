// test_cases.c - 用于测试外部依赖识别的各种场景

#include <stdio.h>      // 标准库头文件
#include <stdlib.h>     // 标准库头文件
#include "mylib.h"      // 用户自定义头文件
#include "config.h"     // 配置头文件

// 测试场景1: 宏定义和使用
#define LOCAL_MACRO 100
#define CALC_MACRO(x) ((x) * 2)

// 测试场景2: 类型定义
typedef struct {
    int x;
    int y;
} Point;

typedef enum {
    STATE_IDLE,
    STATE_RUNNING,
    STATE_STOPPED
} State;

// 测试场景3: 全局变量声明
int global_var = 10;
static int static_var = 20;
extern int external_var;        // 外部变量声明

// 测试场景4: 函数声明和定义
void local_func(int a);         // 本地函数声明
extern void external_func(int b); // 外部函数声明
static void static_func(void);   // 静态函数声明

// 测试场景5: 函数定义
void local_func(int a) {
    printf("Local function: %d\n", a);
}

static void static_func(void) {
    printf("Static function\n");
}

// 测试场景6: 主函数中的各种引用
int main() {
    // 使用标准库函数（来自stdio.h）
    printf("Hello, World!\n");  // 外部函数
    
    // 使用标准库函数（来自stdlib.h）
    void* ptr = malloc(100);    // 外部函数
    free(ptr);                  // 外部函数
    
    // 使用可能来自mylib.h的函数和变量
    int result = mylib_function(42);  // 可能是外部函数（取决于mylib.h）
    mylib_global_var = 100;          // 可能是外部变量（取决于mylib.h）
    
    // 使用可能来自config.h的宏
    #ifdef CONFIG_DEBUG
        printf("Debug mode\n");
    #endif
    
    // 使用本地定义的宏
    int val = LOCAL_MACRO;
    int calc = CALC_MACRO(5);
    
    // 使用本地定义的类型
    Point p = {10, 20};
    State s = STATE_RUNNING;
    
    // 使用本地变量
    global_var = 30;
    
    // 使用外部变量
    external_var = 40;          // 这是真正的外部依赖
    
    // 调用本地函数
    local_func(50);
    static_func();
    
    // 调用外部函数
    external_func(60);          // 这是真正的外部依赖
    
    // 使用未声明的函数和变量（错误情况）
    undefined_func();           // 未声明的外部函数
    undefined_var = 70;         // 未声明的外部变量
    
    // 使用可能的外部类型
    EXTERNAL_TYPE* ext_ptr;     // 未定义的类型
    
    // 使用可能的外部宏
    int config = MAX_BUFFER_SIZE; // 未定义的宏
    
    return 0;
}

// 测试场景7: 另一个函数中的引用
void test_function() {
    // 嵌套作用域
    {
        int local_var = 10;
        printf("Local: %d\n", local_var);
    }
    
    // 函数参数作为本地变量
    void inner_func(int param) {
        param = param + 1;
    }
    
    // 使用全局变量
    global_var++;
    
    // 使用外部资源
    FILE* fp = fopen("test.txt", "r");  // 外部函数和类型
    if (fp) {
        fclose(fp);                      // 外部函数
    }
}

// 测试场景8: 条件编译
#ifdef FEATURE_X
void feature_x_function() {
    feature_x_var = 100;        // 条件编译中的外部变量
    feature_x_init();           // 条件编译中的外部函数
}
#endif

// 测试场景9: 函数指针
void (*func_ptr)(int) = &local_func;  // 本地函数指针
void (*ext_func_ptr)(void) = &unknown_func; // 外部函数指针

// 测试场景10: 复杂声明
int (*array_of_func_ptrs[10])(int, int);
struct complex_struct {
    int (*callback)(void*);
    UNKNOWN_TYPE* data;         // 外部类型
};