# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /root/preprocess/SAST

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /root/preprocess/SAST

# Include any dependencies generated for this target.
include CMakeFiles/pvs-studio-cmake-example-1.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/pvs-studio-cmake-example-1.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/pvs-studio-cmake-example-1.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/pvs-studio-cmake-example-1.dir/flags.make

CMakeFiles/pvs-studio-cmake-example-1.dir/main.cpp.o: CMakeFiles/pvs-studio-cmake-example-1.dir/flags.make
CMakeFiles/pvs-studio-cmake-example-1.dir/main.cpp.o: main.cpp
CMakeFiles/pvs-studio-cmake-example-1.dir/main.cpp.o: CMakeFiles/pvs-studio-cmake-example-1.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/preprocess/SAST/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/pvs-studio-cmake-example-1.dir/main.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/pvs-studio-cmake-example-1.dir/main.cpp.o -MF CMakeFiles/pvs-studio-cmake-example-1.dir/main.cpp.o.d -o CMakeFiles/pvs-studio-cmake-example-1.dir/main.cpp.o -c /root/preprocess/SAST/main.cpp

CMakeFiles/pvs-studio-cmake-example-1.dir/main.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/pvs-studio-cmake-example-1.dir/main.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/preprocess/SAST/main.cpp > CMakeFiles/pvs-studio-cmake-example-1.dir/main.cpp.i

CMakeFiles/pvs-studio-cmake-example-1.dir/main.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/pvs-studio-cmake-example-1.dir/main.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/preprocess/SAST/main.cpp -o CMakeFiles/pvs-studio-cmake-example-1.dir/main.cpp.s

# Object files for target pvs-studio-cmake-example-1
pvs__studio__cmake__example__1_OBJECTS = \
"CMakeFiles/pvs-studio-cmake-example-1.dir/main.cpp.o"

# External object files for target pvs-studio-cmake-example-1
pvs__studio__cmake__example__1_EXTERNAL_OBJECTS =

pvs-studio-cmake-example-1: CMakeFiles/pvs-studio-cmake-example-1.dir/main.cpp.o
pvs-studio-cmake-example-1: CMakeFiles/pvs-studio-cmake-example-1.dir/build.make
pvs-studio-cmake-example-1: CMakeFiles/pvs-studio-cmake-example-1.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/root/preprocess/SAST/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX executable pvs-studio-cmake-example-1"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/pvs-studio-cmake-example-1.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/pvs-studio-cmake-example-1.dir/build: pvs-studio-cmake-example-1
.PHONY : CMakeFiles/pvs-studio-cmake-example-1.dir/build

CMakeFiles/pvs-studio-cmake-example-1.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/pvs-studio-cmake-example-1.dir/cmake_clean.cmake
.PHONY : CMakeFiles/pvs-studio-cmake-example-1.dir/clean

CMakeFiles/pvs-studio-cmake-example-1.dir/depend:
	cd /root/preprocess/SAST && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /root/preprocess/SAST /root/preprocess/SAST /root/preprocess/SAST /root/preprocess/SAST /root/preprocess/SAST/CMakeFiles/pvs-studio-cmake-example-1.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : CMakeFiles/pvs-studio-cmake-example-1.dir/depend

