{"name": "vulnerability-detection-platform", "version": "1.0.0", "private": true, "scripts": {"serve": "export NODE_OPTIONS=--openssl-legacy-provider && vue-cli-service serve", "build": "vue-cli-service build --openssl-legacy-provider", "lint": "vue-cli-service lint"}, "dependencies": {"axios": "^0.24.0", "body-parser": "^2.2.0", "chart.js": "^3.9.1", "child_process": "^1.0.2", "core-js": "^3.6.5", "cors": "^2.8.5", "echarts": "^5.6.0", "element-ui": "^2.15.6", "express": "^5.1.0", "highlight.js": "^11.3.1", "jszip": "^3.10.1", "multer": "^2.0.1", "unzipper": "^0.12.3", "vue": "^2.6.14", "vue-highlightjs": "^1.3.3", "vue-router": "^3.5.1", "vuex": "^3.6.2"}, "devDependencies": {"@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "~4.5.0", "@vue/cli-service": "~4.5.0", "babel-eslint": "^10.1.0", "eslint": "^6.7.2", "eslint-plugin-vue": "^6.2.2", "vue-template-compiler": "^2.6.11"}, "description": "## Project setup ``` npm install ```", "main": "babel.config.js", "keywords": [], "author": "", "license": "ISC"}