#!/usr/bin/env python3
"""
Tree-sitter部署脚本
自动下载、编译和安装tree-sitter及C语言解析器
"""

import os
import sys
import subprocess
import platform
import shutil
import tempfile
import logging
from pathlib import Path
from typing import Optional, Tuple, List
import urllib.request
import zipfile
import json

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class TreeSitterDeployer:
    """Tree-sitter部署器"""
    
    REQUIRED_TREE_SITTER_VERSION = "0.21.3"  # 必须使用的版本
    
    def __init__(self, install_dir: str = None):
        """
        初始化部署器
        
        Args:
            install_dir: 安装目录，默认为用户目录下的.tree-sitter
        """
        if install_dir:
            self.install_dir = Path(install_dir)
        else:
            self.install_dir = Path.home() / '.tree-sitter'
        
        self.install_dir.mkdir(parents=True, exist_ok=True)
        self.languages_dir = self.install_dir / 'languages'
        self.languages_dir.mkdir(exist_ok=True)
        
        self.system = platform.system().lower()
        self.arch = platform.machine().lower()
        
        logger.info(f"安装目录: {self.install_dir}")
        logger.info(f"操作系统: {self.system}")
        logger.info(f"架构: {self.arch}")
    
    def deploy(self) -> bool:
        """
        执行完整的部署流程
        
        Returns:
            部署是否成功
        """
        try:
            # 1. 检查依赖
            if not self._check_dependencies():
                return False
            
            # 2. 安装tree-sitter Python包
            if not self._install_tree_sitter_python():
                return False
            
            # 3. 下载并编译tree-sitter-c
            c_parser_path = self._build_language_parser('c')
            if not c_parser_path:
                return False
            
            # 4. 验证安装
            if not self._verify_installation(c_parser_path):
                return False
            
            # 5. 生成配置文件
            self._generate_config()
            
            logger.info("Tree-sitter部署成功！")
            return True
            
        except Exception as e:
            logger.error(f"部署失败: {str(e)}")
            return False
    
    def _check_dependencies(self) -> bool:
        """检查系统依赖"""
        logger.info("检查系统依赖...")
        
        # 检查Python版本
        if sys.version_info < (3, 6):
            logger.error("需要Python 3.6或更高版本")
            return False
        
        # 检查编译工具
        required_tools = []
        if self.system == 'windows':
            # Windows上检查多种可能的编译器
            has_compiler = any([
                shutil.which('cl.exe'),
                shutil.which('gcc'),
                shutil.which('clang')
            ])
            if not has_compiler:
                logger.error("缺少C编译器")
                logger.info("请安装以下任一编译环境:")
                logger.info("  - Visual Studio Build Tools")
                logger.info("  - MinGW-w64")
                logger.info("  - MSYS2")
                return False
        else:
            required_tools = ['gcc', 'make']
            missing_tools = []
            for tool in required_tools:
                if not shutil.which(tool):
                    missing_tools.append(tool)
            
            if missing_tools:
                logger.error(f"缺少编译工具: {', '.join(missing_tools)}")
                logger.info("请安装: sudo apt-get install build-essential (Ubuntu/Debian)")
                logger.info("或: sudo yum install gcc make (RHEL/CentOS)")
                return False
        
        logger.info("依赖检查通过")
        return True
    
    def _install_tree_sitter_python(self) -> bool:
        """安装tree-sitter Python包"""
        logger.info("安装tree-sitter Python包...")
        
        required_version = self.REQUIRED_TREE_SITTER_VERSION
        
        try:
            import tree_sitter
            current_version = getattr(tree_sitter, '__version__', 'unknown')
            logger.info(f"tree-sitter已安装，版本: {current_version}")
            
            # 检查版本是否正确
            if current_version != required_version:
                logger.warning(f"当前版本 {current_version} 不是所需版本 {required_version}")
                logger.info(f"卸载当前版本并安装 {required_version}...")
                
                # 卸载当前版本
                try:
                    subprocess.check_call([
                        sys.executable, '-m', 'pip', 'uninstall', '-y', 'tree-sitter'
                    ])
                    logger.info("当前版本已卸载")
                except subprocess.CalledProcessError as e:
                    logger.error(f"卸载失败: {e}")
                    return False
                    
                # 安装指定版本
                return self._install_specific_version(required_version)
            
            # 检查是否有Language类
            if hasattr(tree_sitter, 'Language'):
                logger.info(f"tree-sitter {required_version} 已正确安装")
                return True
            else:
                logger.warning("tree-sitter包可能损坏，重新安装...")
                return self._install_specific_version(required_version)
                
        except ImportError:
            logger.info(f"tree-sitter未安装，安装版本 {required_version}...")
            return self._install_specific_version(required_version)
    
    def _install_specific_version(self, version: str) -> bool:
        """安装指定版本的tree-sitter"""
        try:
            # 安装指定版本
            subprocess.check_call([
                sys.executable, '-m', 'pip', 'install', f'tree-sitter=={version}'
            ])
            
            # 验证安装
            import importlib
            import tree_sitter
            importlib.reload(tree_sitter)
            
            installed_version = getattr(tree_sitter, '__version__', 'unknown')
            if installed_version == version:
                logger.info(f"tree-sitter {version} 安装成功")
                return True
            else:
                logger.error(f"版本验证失败，期望 {version}，实际 {installed_version}")
                return False
                
        except subprocess.CalledProcessError as e:
            logger.error(f"安装tree-sitter {version} 失败: {e}")
            return False
        except Exception as e:
            logger.error(f"验证安装失败: {e}")
            return False
    
    def _build_language_parser(self, language: str) -> Optional[Path]:
        """
        下载并编译语言解析器
        
        Args:
            language: 语言名称（如'c', 'cpp', 'python'等）
            
        Returns:
            编译后的.so/.dll文件路径
        """
        logger.info(f"构建{language}语言解析器...")
        
        repo_url = f"https://github.com/tree-sitter/tree-sitter-{language}"
        language_dir = self.languages_dir / f"tree-sitter-{language}"
        
        # 下载语言仓库
        if not language_dir.exists():
            if not self._download_repository(repo_url, language_dir):
                return None
        else:
            logger.info(f"{language}解析器源码已存在")
        
        # 编译解析器
        return self._compile_parser(language_dir, language)
    
    def _download_repository(self, repo_url: str, target_dir: Path) -> bool:
        """下载GitHub仓库"""
        logger.info(f"下载仓库: {repo_url}")
        
        # 尝试使用git clone
        if shutil.which('git'):
            try:
                subprocess.check_call([
                    'git', 'clone', '--depth', '1', repo_url, str(target_dir)
                ])
                logger.info("仓库下载成功")
                return True
            except subprocess.CalledProcessError:
                logger.warning("git clone失败，尝试下载压缩包")
        
        # 下载zip压缩包
        zip_url = f"{repo_url}/archive/refs/heads/master.zip"
        return self._download_and_extract(zip_url, target_dir)
    
    def _download_and_extract(self, url: str, target_dir: Path) -> bool:
        """下载并解压文件"""
        try:
            with tempfile.TemporaryDirectory() as temp_dir:
                temp_path = Path(temp_dir)
                
                # 下载文件
                zip_path = temp_path / "download.zip"
                logger.info(f"下载: {url}")
                urllib.request.urlretrieve(url, zip_path)
                
                # 解压文件
                logger.info("解压文件...")
                with zipfile.ZipFile(zip_path, 'r') as zip_file:
                    zip_file.extractall(temp_path)
                
                # 移动到目标目录
                extracted_dirs = list(temp_path.glob("tree-sitter-*"))
                if extracted_dirs:
                    shutil.move(str(extracted_dirs[0]), str(target_dir))
                    logger.info("解压成功")
                    return True
                else:
                    logger.error("解压失败：未找到预期的目录")
                    return False
                    
        except Exception as e:
            logger.error(f"下载失败: {str(e)}")
            return False
    
    def _compile_parser(self, language_dir: Path, language: str) -> Optional[Path]:
        """编译语言解析器"""
        logger.info(f"编译{language}解析器...")
        
        # 确定输出文件名
        if self.system == 'windows':
            output_file = self.install_dir / f"tree-sitter-{language}.dll"
        else:
            output_file = self.install_dir / f"tree-sitter-{language}.so"
        
        # 查找源文件
        src_dir = language_dir / 'src'
        if not src_dir.exists():
            logger.error(f"未找到源码目录: {src_dir}")
            return None
        
        # 收集所有需要编译的C文件
        c_files = list(src_dir.glob('*.c'))
        if not c_files:
            logger.error(f"未找到C源文件在: {src_dir}")
            return None
        
        # 编译方法1：使用新版tree-sitter API
        try:
            import tree_sitter
            
            # 检查是否有build_library方法
            if hasattr(tree_sitter, 'build_library'):
                # 新版API
                tree_sitter.build_library(
                    str(output_file),
                    [str(language_dir)]
                )
                logger.info(f"使用tree-sitter.build_library编译成功")
                return output_file
                
            # 如果是Language类的静态方法
            elif hasattr(tree_sitter, 'Language') and hasattr(tree_sitter.Language, 'build_library'):
                tree_sitter.Language.build_library(
                    str(output_file),
                    [str(language_dir)]
                )
                logger.info(f"使用Language.build_library编译成功")
                return output_file
                
        except Exception as e:
            logger.warning(f"tree-sitter API编译失败: {e}")
            logger.info("尝试手动编译...")
        
        # 编译方法2：手动编译
        return self._manual_compile(c_files, output_file, src_dir)
    
    def _manual_compile(self, c_files: List[Path], output_file: Path, src_dir: Path) -> Optional[Path]:
        """手动编译parser"""
        logger.info("使用手动编译方式...")
        
        # 准备编译命令
        if self.system == 'windows':
            # Windows编译
            if shutil.which('gcc'):
                # 使用MinGW
                compile_cmd = [
                    'gcc',
                    '-shared',
                    '-o', str(output_file),
                    '-I', str(src_dir),
                    '-fPIC'
                ] + [str(f) for f in c_files]
            elif shutil.which('cl.exe'):
                # 使用MSVC
                compile_cmd = [
                    'cl.exe',
                    '/LD',
                    f'/Fe{str(output_file)}',
                    f'/I{str(src_dir)}'
                ] + [str(f) for f in c_files]
            else:
                logger.error("未找到合适的编译器")
                return None
        else:
            # Unix/Linux编译
            compile_cmd = [
                'gcc',
                '-shared',
                '-fPIC',
                '-o', str(output_file),
                '-I', str(src_dir)
            ] + [str(f) for f in c_files]
        
        try:
            logger.info(f"执行编译命令: {' '.join(compile_cmd)}")
            subprocess.check_call(compile_cmd, cwd=str(src_dir))
            logger.info(f"编译成功: {output_file}")
            return output_file
            
        except subprocess.CalledProcessError as e:
            logger.error(f"编译失败: {e}")
            
            # 如果第一次失败，尝试更简单的编译选项
            if self.system != 'windows':
                simple_cmd = [
                    'gcc',
                    '-shared',
                    '-o', str(output_file)
                ] + [str(f) for f in c_files]
                
                try:
                    logger.info("尝试简化的编译命令...")
                    subprocess.check_call(simple_cmd, cwd=str(src_dir))
                    logger.info("简化编译成功")
                    return output_file
                except subprocess.CalledProcessError:
                    pass
            
            return None
    
    def _verify_installation(self, parser_path: Path) -> bool:
        """验证安装是否成功"""
        logger.info("验证安装...")
        
        try:
            import tree_sitter
            from tree_sitter import Language, Parser
            
            # 加载语言
            C_LANGUAGE = Language(str(parser_path), 'c')
            
            # 创建解析器
            parser = Parser()
            parser.set_language(C_LANGUAGE)
            
            # 测试解析
            test_code = b"int main() { return 0; }"
            tree = parser.parse(test_code)
            
            if tree.root_node:
                logger.info("验证成功：解析器正常工作")
                return True
            else:
                logger.error("验证失败：无法解析测试代码")
                return False
                
        except Exception as e:
            logger.error(f"验证失败: {str(e)}")
            return False
    
    def _generate_config(self):
        """生成配置文件"""
        config_path = self.install_dir / 'config.py'
        
        config_content = f"""# Tree-sitter配置文件
# 自动生成，请勿手动修改

from pathlib import Path

TREE_SITTER_DIR = Path(r"{self.install_dir}")
LANGUAGES_DIR = TREE_SITTER_DIR / "languages"

# 语言解析器路径
PARSERS = {{
    'c': TREE_SITTER_DIR / "tree-sitter-c.{'dll' if self.system == 'windows' else 'so'}",
    # 在这里添加其他语言
}}

def get_parser_path(language: str) -> Path:
    \"\"\"获取指定语言的解析器路径\"\"\"
    return PARSERS.get(language)
"""
        
        with open(config_path, 'w', encoding='utf-8') as f:
            f.write(config_content)
        
        logger.info(f"配置文件已生成: {config_path}")
    
    def add_language(self, language: str) -> bool:
        """
        添加新的语言支持
        
        Args:
            language: 语言名称
            
        Returns:
            是否成功
        """
        logger.info(f"添加{language}语言支持...")
        
        parser_path = self._build_language_parser(language)
        if parser_path:
            logger.info(f"{language}语言支持添加成功")
            self._generate_config()  # 更新配置
            return True
        else:
            logger.error(f"{language}语言支持添加失败")
            return False
    
    def list_supported_languages(self) -> list:
        """列出所有支持的语言"""
        parsers = []
        
        # 查找已编译的解析器
        for file in self.install_dir.glob("tree-sitter-*.*"):
            if file.suffix in ['.so', '.dll']:
                language = file.stem.replace('tree-sitter-', '')
                parsers.append(language)
        
        return parsers


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Tree-sitter部署工具')
    parser.add_argument(
        '--install-dir',
        help='安装目录',
        default=None
    )
    parser.add_argument(
        '--add-language',
        help='添加额外的语言支持',
        action='append',
        default=[]
    )
    parser.add_argument(
        '--list',
        help='列出已安装的语言',
        action='store_true'
    )
    
    args = parser.parse_args()
    
    deployer = TreeSitterDeployer(args.install_dir)
    
    if args.list:
        languages = deployer.list_supported_languages()
        print("\n已安装的语言:")
        for lang in languages:
            print(f"  - {lang}")
        return
    
    # 执行部署
    if deployer.deploy():
        # 添加额外的语言
        for language in args.add_language:
            deployer.add_language(language)
        
        print("\n=== 部署成功 ===")
        print(f"安装目录: {deployer.install_dir}")
        print(f"C解析器路径: {deployer.install_dir / 'tree-sitter-c.so'}")
        print(f"tree-sitter版本: {deployer.REQUIRED_TREE_SITTER_VERSION}")
        print("\n使用示例:")
        print(f"from tree_sitter import Language, Parser")
        print(f"C_LANGUAGE = Language('{deployer.install_dir / 'tree-sitter-c.so'}', 'c')")
    else:
        print("\n部署失败，请查看日志")
        sys.exit(1)


if __name__ == "__main__":
    main()
