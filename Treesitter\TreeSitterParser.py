"""
Tree-Sitter C语言解析器
用于分析C代码文件中的外部依赖（函数、变量、类型、宏）
"""

import logging
from typing import Dict, List, Any, Optional, Set, Tuple
from dataclasses import dataclass
from tree_sitter import Language, Parser

logger = logging.getLogger(__name__)


@dataclass
class ExternalReference:
    """外部引用信息"""
    name: str
    type: str  # function, variable, type, macro
    file_path: str
    line_number: int
    context: str


class TreeSitterParser:
    """Tree-Sitter代码解析器"""
    
    def __init__(self, language_path: str = None):
        """
        初始化解析器
        
        Args:
            language_path: tree-sitter-c.so 文件路径
        """
        if language_path:
            try:
                C_LANGUAGE = Language(language_path, 'c')
                self.parser = Parser()
                self.parser.set_language(C_LANGUAGE)
                logger.info("Tree-Sitter解析器初始化成功")
            except Exception as e:
                logger.error(f"加载Tree-Sitter语言失败: {str(e)}")
                self.parser = None
        else:
            self.parser = None
            logger.warning("未提供language_path，Tree-Sitter解析器未初始化")
    
    def extract_external_references(self, file_path: str) -> List[ExternalReference]:
        """
        提取代码文件中的外部引用（使用but未定义的变量、函数和宏）
        
        Args:
            file_path: C源文件路径
            
        Returns:
            外部引用列表
        """
        if not self.parser:
            logger.error("解析器未初始化")
            return []
            
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                source_code = f.read()
            
            # 将源代码转换为字节
            source_bytes = source_code.encode('utf-8')
            tree = self.parser.parse(source_bytes)
            
            # 收集定义的符号
            defined_symbols = self._collect_defined_symbols(tree.root_node, source_bytes)
            logger.debug(f"Defined symbols: {defined_symbols}")
            
            # 收集使用的符号
            used_symbols = []
            self._collect_used_symbols(tree.root_node, source_bytes, file_path, used_symbols)
            
            # 过滤出外部引用
            external_references = []
            for ref in used_symbols:
                # 检查是否在对应类型的定义集合中
                if ref.name not in defined_symbols.get(ref.type, set()):
                    # 额外检查：函数调用可能实际是已定义的函数指针或宏
                    if ref.type == 'function' and (
                        ref.name in defined_symbols.get('variable', set()) or
                        ref.name in defined_symbols.get('macro', set())
                    ):
                        continue
                    external_references.append(ref)
            
            # 去重
            unique_refs = self._deduplicate_references(external_references)
            
            logger.info(f"从 {file_path} 提取了 {len(unique_refs)} 个外部引用")
            return unique_refs
            
        except Exception as e:
            logger.error(f"解析文件失败 {file_path}: {str(e)}")
            import traceback
            traceback.print_exc()
            return []
    
    def analyze_external_dependencies(self, file_path: str) -> Dict[str, List[Dict]]:
        """
        分析文件的外部依赖，返回分类后的结果
        
        Args:
            file_path: C源文件路径
            
        Returns:
            分类后的外部依赖字典
        """
        references = self.extract_external_references(file_path)
        
        result = {
            'external_functions': [],
            'external_variables': [],
            'external_types': [],
            'external_macros': []
        }
        
        for ref in references:
            ref_info = {
                'name': ref.name,
                'line': ref.line_number,
                'context': ref.context
            }
            
            if ref.type == 'function':
                result['external_functions'].append(ref_info)
            elif ref.type == 'variable':
                result['external_variables'].append(ref_info)
            elif ref.type == 'type':
                result['external_types'].append(ref_info)
            elif ref.type == 'macro':
                result['external_macros'].append(ref_info)
        
        return result
    
    def _get_node_text(self, node, source_bytes: bytes) -> str:
        """安全地获取节点文本"""
        try:
            return source_bytes[node.start_byte:node.end_byte].decode('utf-8')
        except Exception as e:
            logger.error(f"获取节点文本失败: {e}")
            return ""
    
    def _collect_defined_symbols(self, node, source_bytes: bytes) -> Dict[str, Set[str]]:
        """收集文件中定义的所有符号"""
        defined = {
            'function': set(),
            'variable': set(),
            'type': set(),
            'macro': set()
        }
        
        self._traverse_for_definitions(node, source_bytes, defined)
        
        # 添加C标准类型
        standard_types = {'FILE', 'size_t', 'ptrdiff_t', 'wchar_t', 'time_t', 'clock_t'}
        defined['type'].update(standard_types)
        
        return defined
    
    def _traverse_for_definitions(self, node, source_bytes: bytes, defined: Dict[str, Set[str]]):
        """遍历AST收集定义"""
        
        # 函数定义
        if node.type == 'function_definition':
            declarator = node.child_by_field_name('declarator')
            if declarator:
                func_name = self._extract_function_name(declarator, source_bytes)
                if func_name:
                    defined['function'].add(func_name)
                    logger.debug(f"Found function definition: {func_name}")
        
        # 变量定义和声明
        elif node.type == 'declaration':
            # 检查是否是extern声明
            is_extern = False
            is_typedef = False
            
            for child in node.children:
                if child.type == 'storage_class_specifier':
                    storage_class = self._get_node_text(child, source_bytes)
                    if storage_class == 'extern':
                        is_extern = True
                    elif storage_class == 'typedef':
                        is_typedef = True
            
            if is_typedef:
                # typedef声明 - 提取类型名
                declarators = self._extract_declarators(node, source_bytes)
                for type_name in declarators:
                    defined['type'].add(type_name)
                    logger.debug(f"Found typedef: {type_name}")
            elif not is_extern:
                # 非extern变量声明
                declarators = self._extract_declarators(node, source_bytes)
                for var_name in declarators:
                    defined['variable'].add(var_name)
                    logger.debug(f"Found variable declaration: {var_name}")
        
        # 参数声明
        elif node.type == 'parameter_declaration':
            declarator = node.child_by_field_name('declarator')
            if declarator:
                var_name = self._extract_identifier_from_declarator(declarator, source_bytes)
                if var_name:
                    defined['variable'].add(var_name)
        
        # 结构体/联合体/枚举定义
        elif node.type in ['struct_specifier', 'union_specifier', 'enum_specifier']:
            name_node = node.child_by_field_name('name')
            if name_node:
                type_name = self._get_node_text(name_node, source_bytes)
                # 添加带前缀的完整类型名
                prefix = node.type.split('_')[0]  # struct, union, enum
                defined['type'].add(f"{prefix} {type_name}")
                defined['type'].add(type_name)  # 也添加不带前缀的
                logger.debug(f"Found {prefix} definition: {type_name}")
        
        # 宏定义
        elif node.type == 'preproc_def':
            name_node = node.child_by_field_name('name')
            if name_node:
                macro_name = self._get_node_text(name_node, source_bytes)
                defined['macro'].add(macro_name)
                logger.debug(f"Found macro definition: {macro_name}")
        
        # 枚举常量
        elif node.type == 'enumerator':
            name_node = node.child_by_field_name('name')
            if name_node:
                enum_const = self._get_node_text(name_node, source_bytes)
                defined['variable'].add(enum_const)  # 枚举常量视为变量
        
        # 函数声明（非定义）
        elif node.type == 'function_declarator' and node.parent and node.parent.type == 'declaration':
            # 确保不是函数定义的一部分
            is_definition = False
            current = node
            while current:
                if current.type == 'function_definition':
                    is_definition = True
                    break
                current = current.parent
            
            if not is_definition:
                func_name = self._extract_function_name(node, source_bytes)
                if func_name and not self._is_declaration_extern(node.parent, source_bytes):
                    defined['function'].add(func_name)
                    logger.debug(f"Found function declaration: {func_name}")
        
        # 递归遍历子节点
        for child in node.children:
            self._traverse_for_definitions(child, source_bytes, defined)
    
    def _is_declaration_extern(self, declaration_node, source_bytes: bytes) -> bool:
        """检查声明是否包含extern关键字"""
        for child in declaration_node.children:
            if child.type == 'storage_class_specifier':
                if self._get_node_text(child, source_bytes) == 'extern':
                    return True
        return False
    
    def _collect_used_symbols(self, node, source_bytes: bytes, file_path: str, 
                            used_symbols: List[ExternalReference], parent_context: Optional[str] = None):
        """收集使用的符号"""
        
        # 跳过预处理指令内部的内容（除了条件编译中的表达式）
        if parent_context == 'preproc' and node.type not in ['preproc_ifdef', 'preproc_ifndef', 'preproc_if']:
            for child in node.children:
                context = 'preproc' if node.type.startswith('preproc_') else parent_context
                self._collect_used_symbols(child, source_bytes, file_path, used_symbols, context)
            return
        
        # 函数调用
        if node.type == 'call_expression':
            function_node = node.child_by_field_name('function')
            if function_node:
                if function_node.type == 'identifier':
                    func_name = self._get_node_text(function_node, source_bytes)
                    if not self._is_c_keyword_or_builtin(func_name):
                        used_symbols.append(ExternalReference(
                            name=func_name,
                            type='function',
                            file_path=file_path,
                            line_number=function_node.start_point[0] + 1,
                            context=self._get_line_context(function_node, source_bytes)
                        ))
                elif function_node.type == 'field_expression':
                    # 处理结构体成员函数指针调用，如 ptr->func()
                    field_node = function_node.child_by_field_name('field')
                    if field_node:
                        self._collect_used_symbols(field_node, source_bytes, file_path, used_symbols, parent_context)
        
        # 标识符
        elif node.type == 'identifier':
            identifier = self._get_node_text(node, source_bytes)
            parent = node.parent
            
            # 跳过定义中的标识符
            if self._is_identifier_in_definition(node, parent):
                pass
            # 跳过函数调用（已在上面处理）
            elif parent and parent.type == 'call_expression' and parent.child_by_field_name('function') == node:
                pass
            # 跳过成员访问
            elif parent and parent.type == 'field_expression' and parent.child_by_field_name('field') == node:
                pass
            # 跳过goto标签
            elif parent and parent.type == 'labeled_statement':
                pass
            else:
                if identifier and not self._is_c_keyword_or_builtin(identifier):
                    ref_type = self._infer_reference_type(node, source_bytes)
                    used_symbols.append(ExternalReference(
                        name=identifier,
                        type=ref_type,
                        file_path=file_path,
                        line_number=node.start_point[0] + 1,
                        context=self._get_line_context(node, source_bytes)
                    ))
        
        # 类型标识符
        elif node.type == 'type_identifier':
            type_name = self._get_node_text(node, source_bytes)
            if not self._is_c_keyword_or_builtin(type_name):
                used_symbols.append(ExternalReference(
                    name=type_name,
                    type='type',
                    file_path=file_path,
                    line_number=node.start_point[0] + 1,
                    context=self._get_line_context(node, source_bytes)
                ))
        
        # 预处理标识符（在条件编译中）
        elif node.type == 'preproc_identifier' and parent_context != 'preproc':
            identifier = self._get_node_text(node, source_bytes)
            if identifier:
                used_symbols.append(ExternalReference(
                    name=identifier,
                    type='macro',
                    file_path=file_path,
                    line_number=node.start_point[0] + 1,
                    context=self._get_line_context(node, source_bytes)
                ))
        
        # 递归遍历
        for child in node.children:
            context = 'preproc' if node.type.startswith('preproc_') else parent_context
            self._collect_used_symbols(child, source_bytes, file_path, used_symbols, context)
    
    def _is_identifier_in_definition(self, node, parent) -> bool:
        """判断标识符是否在定义中"""
        if not parent:
            return False
        
        # 检查是否是声明器的一部分
        current = node
        while current:
            parent_type = current.parent.type if current.parent else None
            
            # 在函数定义的声明器中
            if parent_type == 'function_definition':
                declarator = current.parent.child_by_field_name('declarator')
                if self._is_node_ancestor_of(declarator, node):
                    return True
            
            # 在变量声明的声明器中
            elif parent_type in ['declaration', 'field_declaration']:
                # 但不包括类型部分的标识符
                for child in current.parent.children:
                    if child.type in ['init_declarator', 'declarator', 'field_declarator']:
                        if self._is_node_ancestor_of(child, node):
                            return True
            
            # 在参数声明中
            elif parent_type == 'parameter_declaration':
                declarator = current.parent.child_by_field_name('declarator')
                if declarator and self._is_node_ancestor_of(declarator, node):
                    return True
            
            # 在typedef声明中
            elif parent_type == 'type_definition':
                for child in current.parent.children:
                    if child.type == 'type_declarator':
                        if self._is_node_ancestor_of(child, node):
                            return True
            
            current = current.parent
            if not current or current.type == 'translation_unit':
                break
        
        return False
    
    def _is_node_ancestor_of(self, ancestor, node) -> bool:
        """检查ancestor是否是node的祖先节点"""
        current = node
        while current:
            if current == ancestor:
                return True
            current = current.parent
        return False
    
    def _extract_function_name(self, declarator, source_bytes: bytes) -> Optional[str]:
        """从函数声明器中提取函数名"""
        if declarator.type == 'function_declarator':
            inner_declarator = declarator.child_by_field_name('declarator')
            if inner_declarator:
                return self._extract_function_name(inner_declarator, source_bytes)
        elif declarator.type == 'pointer_declarator':
            inner_declarator = declarator.child_by_field_name('declarator')
            if inner_declarator:
                return self._extract_function_name(inner_declarator, source_bytes)
        elif declarator.type == 'identifier':
            return self._get_node_text(declarator, source_bytes)
        elif declarator.type == 'array_declarator':
            inner_declarator = declarator.child_by_field_name('declarator')
            if inner_declarator:
                return self._extract_function_name(inner_declarator, source_bytes)
        return None
    
    def _extract_declarators(self, declaration_node, source_bytes: bytes) -> List[str]:
        """从声明中提取所有声明的标识符"""
        names = []
        
        def extract_from_node(node):
            if node.type == 'init_declarator':
                declarator = node.child_by_field_name('declarator')
                if declarator:
                    name = self._extract_identifier_from_declarator(declarator, source_bytes)
                    if name:
                        names.append(name)
            elif node.type in ['declarator', 'field_declarator', 'type_declarator']:
                name = self._extract_identifier_from_declarator(node, source_bytes)
                if name:
                    names.append(name)
            
            # 递归处理子节点
            for child in node.children:
                extract_from_node(child)
        
        extract_from_node(declaration_node)
        return names
    
    def _extract_identifier_from_declarator(self, declarator, source_bytes: bytes) -> Optional[str]:
        """从声明器中提取标识符"""
        if declarator.type == 'identifier':
            return self._get_node_text(declarator, source_bytes)
        elif declarator.type == 'field_identifier':
            return self._get_node_text(declarator, source_bytes)
        elif declarator.type in ['pointer_declarator', 'array_declarator', 'function_declarator', 
                                 'parenthesized_declarator', 'attributed_declarator']:
            # 查找嵌套的声明器
            for child in declarator.children:
                if child.type in ['declarator', 'field_declarator', 'type_declarator'] or \
                   child == declarator.child_by_field_name('declarator'):
                    result = self._extract_identifier_from_declarator(child, source_bytes)
                    if result:
                        return result
            # 如果没有找到嵌套声明器，继续查找标识符
            for child in declarator.children:
                if child.type in ['identifier', 'field_identifier']:
                    return self._get_node_text(child, source_bytes)
        return None
    
    def _is_c_keyword_or_builtin(self, identifier: str) -> bool:
        """判断是否是C关键字或内置类型"""
        keywords = {
            # 控制流
            'if', 'else', 'switch', 'case', 'default', 'for', 'while', 'do', 
            'break', 'continue', 'return', 'goto',
            # 类型
            'void', 'char', 'short', 'int', 'long', 'float', 'double', 
            'signed', 'unsigned', 'struct', 'union', 'enum', 'typedef',
            '_Bool', '_Complex', '_Imaginary',
            # 存储类
            'auto', 'register', 'static', 'extern', 'const', 'volatile',
            'inline', 'restrict', '_Alignas', '_Alignof', '_Atomic',
            '_Thread_local', '_Noreturn',
            # 其他
            'sizeof', 'NULL', 'true', 'false',
            # 标准库函数（部分常见的）
            'printf', 'scanf', 'fprintf', 'fscanf', 'sprintf', 'sscanf',
            'fopen', 'fclose', 'fread', 'fwrite', 'fgetc', 'fputc',
            'malloc', 'calloc', 'realloc', 'free',
            'strlen', 'strcpy', 'strcmp', 'strcat', 'memcpy', 'memset',
        }
        return identifier in keywords
    
    def _infer_reference_type(self, node, source_bytes: bytes) -> str:
        """根据上下文推断引用类型"""
        parent = node.parent
        if not parent:
            return 'variable'
        
        identifier = self._get_node_text(node, source_bytes)
        
        # 全大写+下划线通常是宏
        if identifier and len(identifier) > 1 and identifier.isupper() and ('_' in identifier or identifier.isalpha()):
            return 'macro'
        
        # 在类型转换中
        if parent.type == 'cast_expression':
            type_desc = parent.child_by_field_name('type')
            if type_desc and self._is_node_ancestor_of(type_desc, node):
                return 'type'
        
        # 在sizeof中
        if parent.type == 'sizeof_expression':
            # sizeof(type) vs sizeof(expression)
            if parent.children[0].type == '(' and len(parent.children) > 2:
                # 检查是否看起来像类型
                if self._looks_like_type(node, parent):
                    return 'type'
        
        # 作为声明中的类型
        current = parent
        while current:
            if current.type in ['declaration', 'parameter_declaration', 'field_declaration']:
                # 检查是否在类型描述部分
                for child in current.children:
                    if child.type in ['struct_specifier', 'union_specifier', 'enum_specifier']:
                        continue
                    if child.type in ['type_qualifier', 'storage_class_specifier']:
                        continue
                    if self._is_node_ancestor_of(child, node) and child.type != 'init_declarator':
                        # 可能是类型标识符
                        if node.parent.type != 'pointer_declarator':
                            return 'type'
                    break
                break
            current = current.parent
        
        return 'variable'
    
    def _looks_like_type(self, node, parent) -> bool:
        """检查节点是否看起来像类型"""
        # 简单的启发式：如果标识符后面跟着*或标识符，可能是类型
        node_index = parent.children.index(node) if node in parent.children else -1
        if node_index >= 0 and node_index < len(parent.children) - 1:
            next_node = parent.children[node_index + 1]
            if next_node.type in ['*', 'identifier']:
                return True
        return False
    
    def _get_line_context(self, node, source_bytes: bytes, context_lines: int = 0) -> str:
        """获取节点所在行的上下文"""
        try:
            source_code = source_bytes.decode('utf-8')
            lines = source_code.split('\n')
            line_index = node.start_point[0]
            
            if 0 <= line_index < len(lines):
                # 获取当前行
                result_lines = []
                
                # 添加前面的上下文行
                for i in range(max(0, line_index - context_lines), line_index):
                    result_lines.append(lines[i].rstrip())
                
                # 添加当前行（高亮标记）
                current_line = lines[line_index].rstrip()
                result_lines.append(current_line)
                
                # 添加后面的上下文行
                for i in range(line_index + 1, min(len(lines), line_index + context_lines + 1)):
                    result_lines.append(lines[i].rstrip())
                
                return '\n'.join(result_lines)
        except Exception as e:
            logger.error(f"获取行上下文失败: {e}")
        
        return ""
    
    def _deduplicate_references(self, references: List[ExternalReference]) -> List[ExternalReference]:
        """去除重复的引用"""
        seen = {}
        unique_refs = []
        
        for ref in references:
            key = (ref.name, ref.type)
            if key not in seen:
                seen[key] = ref
                unique_refs.append(ref)
            else:
                # 保留第一次出现的位置，但更新上下文if needed
                existing = seen[key]
                if len(ref.context) > len(existing.context):
                    existing.context = ref.context
        
        return unique_refs
    
    def get_summary_report(self, file_path: str) -> str:
        """生成外部依赖的摘要报告"""
        dependencies = self.analyze_external_dependencies(file_path)
        
        report = f"=== 外部依赖分析报告 ===\n"
        report += f"文件: {file_path}\n\n"
        
        total = sum(len(deps) for deps in dependencies.values())
        report += f"总计外部依赖: {total}\n\n"
        
        for dep_type, dep_list in dependencies.items():
            if dep_list:
                report += f"{dep_type} ({len(dep_list)}):\n"
                for dep in dep_list:
                    report += f"  - {dep['name']} (line {dep['line']})\n"
                    if dep['context']:
                        report += f"    上下文: {dep['context']}\n"
                report += "\n"
        
        return report


# 使用示例
if __name__ == "__main__":
    # 设置日志级别
    logging.basicConfig(level=logging.INFO)
    
    parser = TreeSitterParser("/opt/tree-sitter/languages/tree-sitter-c/build/my-languages.so")
    
    # 分析单个文件
    file_path = "./test.c"
    
    # 获取详细的依赖信息
    dependencies = parser.analyze_external_dependencies(file_path)
    
    print("\n=== 外部依赖分析结果 ===")
    print(f"\n外部函数 ({len(dependencies['external_functions'])}):")
    for func in dependencies['external_functions']:
        print(f"  - {func['name']} (line {func['line']})")
        if func['context']:
            print(f"    {func['context']}")
    
    print(f"\n外部变量 ({len(dependencies['external_variables'])}):")
    for var in dependencies['external_variables']:
        print(f"  - {var['name']} (line {var['line']})")
        if var['context']:
            print(f"    {var['context']}")
    
    print(f"\n外部类型 ({len(dependencies['external_types'])}):")
    for typ in dependencies['external_types']:
        print(f"  - {typ['name']} (line {typ['line']})")
        if typ['context']:
            print(f"    {typ['context']}")
    
    print(f"\n外部宏 ({len(dependencies['external_macros'])}):")
    for macro in dependencies['external_macros']:
        print(f"  - {macro['name']} (line {macro['line']})")
        if macro['context']:
            print(f"    {macro['context']}")
    
    # 生成摘要报告
    print("\n" + parser.get_summary_report(file_path))