#!/bin/bash

# 创建项目目录
PROJECT_DIR="cwe_demo_project"
mkdir -p $PROJECT_DIR
cd $PROJECT_DIR

# 创建 buffer_overflow.h
cat > buffer_overflow.h << 'EOF'
#ifndef BUFFER_OVERFLOW_H
#define BUFFER_OVERFLOW_H

void process_user_input(char* input);
char* get_user_data(void);

#endif
EOF

# 创建 buffer_overflow.c
cat > buffer_overflow.c << 'EOF'
#include <stdio.h>
#include <string.h>
#include "buffer_overflow.h"
#include "memory_leak.h"

// CWE-120: Buffer Copy without Checking Size of Input
void process_user_input(char* input) {
    char buffer[64];
    strcpy(buffer, input);  // 危险：未检查输入长度，可能导致缓冲区溢出
    printf("Processed: %s\n", buffer);
    
    // 调用memory_leak.c中的函数
    store_data(buffer);
}

char* get_user_data(void) {
    static char data[128] = "Sample user data";
    return data;
}
EOF

# 创建 memory_leak.h
cat > memory_leak.h << 'EOF'
#ifndef MEMORY_LEAK_H
#define MEMORY_LEAK_H

void store_data(const char* data);
char* allocate_buffer(int size);

#endif
EOF

# 创建 memory_leak.c
cat > memory_leak.c << 'EOF'
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include "memory_leak.h"
#include "integer_overflow.h"

// CWE-401: Missing Release of Memory after Effective Lifetime
void store_data(const char* data) {
    char* buffer = (char*)malloc(256);  // 分配内存
    if (buffer != NULL) {
        strcpy(buffer, data);
        printf("Data stored: %s\n", buffer);
        // 错误：未释放分配的内存，导致内存泄漏
        // 应该调用 free(buffer);
    }
    
    // 调用integer_overflow.c中的函数
    int size = calculate_buffer_size(strlen(data));
    printf("Calculated size: %d\n", size);
}

char* allocate_buffer(int size) {
    return (char*)malloc(size);
}
EOF

# 创建 integer_overflow.h
cat > integer_overflow.h << 'EOF'
#ifndef INTEGER_OVERFLOW_H
#define INTEGER_OVERFLOW_H

int calculate_buffer_size(int input_size);
void process_array(int* array, int size);

#endif
EOF

# 创建 integer_overflow.c
cat > integer_overflow.c << 'EOF'
#include <stdio.h>
#include <limits.h>
#include "integer_overflow.h"
#include "null_pointer.h"

// CWE-190: Integer Overflow or Wraparound
int calculate_buffer_size(int input_size) {
    int buffer_size = input_size * 100;  // 危险：可能导致整数溢出
    
    // 如果input_size很大，乘以100可能超过INT_MAX
    printf("Input size: %d, Buffer size: %d\n", input_size, buffer_size);
    
    // 调用null_pointer.c中的函数
    process_string(NULL);  // 故意传递NULL
    
    return buffer_size;
}

void process_array(int* array, int size) {
    for (int i = 0; i <= size; i++) {  // 错误：应该是 i < size
        printf("Array[%d] = %d\n", i, array[i]);
    }
}
EOF

# 创建 null_pointer.h
cat > null_pointer.h << 'EOF'
#ifndef NULL_POINTER_H
#define NULL_POINTER_H

void process_string(char* str);
int get_string_length(const char* str);

#endif
EOF

# 创建 null_pointer.c
cat > null_pointer.c << 'EOF'
#include <stdio.h>
#include <string.h>
#include "null_pointer.h"
#include "format_string.h"

// CWE-476: NULL Pointer Dereference
void process_string(char* str) {
    // 危险：未检查str是否为NULL就直接使用
    int len = strlen(str);  // 如果str为NULL，会导致段错误
    printf("String length: %d\n", len);
    
    // 调用format_string.c中的函数
    if (str != NULL) {
        print_user_message(str);
    } else {
        print_user_message("Default message");
    }
}

int get_string_length(const char* str) {
    // 同样的问题：未检查NULL
    return strlen(str);
}
EOF

# 创建 format_string.h
cat > format_string.h << 'EOF'
#ifndef FORMAT_STRING_H
#define FORMAT_STRING_H

void print_user_message(const char* message);
void log_message(const char* format, ...);

#endif
EOF

# 创建 format_string.c
cat > format_string.c << 'EOF'
#include <stdio.h>
#include <stdarg.h>
#include "format_string.h"
#include "buffer_overflow.h"

// CWE-134: Use of Externally-Controlled Format String
void print_user_message(const char* message) {
    // 危险：直接将用户输入作为格式字符串
    printf(message);  // 应该使用 printf("%s", message);
    printf("\n");
    
    // 调用buffer_overflow.c中的函数，形成循环依赖
    char* data = get_user_data();
    printf("Got data: %s\n", data);
}

void log_message(const char* format, ...) {
    va_list args;
    va_start(args, format);
    vprintf(format, args);  // 如果format来自用户输入，可能存在格式字符串漏洞
    va_end(args);
}
EOF

# 创建 main.c
cat > main.c << 'EOF'
#include <stdio.h>
#include <stdlib.h>
#include <limits.h>
#include "buffer_overflow.h"
#include "memory_leak.h"
#include "integer_overflow.h"
#include "null_pointer.h"
#include "format_string.h"

int main() {
    printf("=== CWE Security Vulnerabilities Demo ===\n\n");
    
    // 测试缓冲区溢出
    printf("1. Testing Buffer Overflow (CWE-120):\n");
    char large_input[] = "This is a very long string that will definitely overflow the buffer in the process_user_input function";
    process_user_input(large_input);
    
    printf("\n2. Testing Memory Leak (CWE-401):\n");
    store_data("Memory leak test");
    
    printf("\n3. Testing Integer Overflow (CWE-190):\n");
    calculate_buffer_size(INT_MAX / 50);
    
    printf("\n4. Testing NULL Pointer Dereference (CWE-476):\n");
    process_string(NULL);
    
    printf("\n5. Testing Format String Vulnerability (CWE-134):\n");
    print_user_message("User input: %x %x %x %x");
    
    return 0;
}
EOF