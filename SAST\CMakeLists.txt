cmake_minimum_required(VERSION 3.5)
project(pvs-studio-cmake-example-1 CXX)

# 指定 PVS-Studio CMake 模块路径
set(PVS_CMAKE_MODULE_PATH "${CMAKE_SOURCE_DIR}/build/_deps/pvs_cmakemodule-src")

# 引入 PVS-Studio 静态分析模块
include("${PVS_CMAKE_MODULE_PATH}/PVS-Studio.cmake")

# 添加可执行文件
add_executable(${PROJECT_NAME} main.cpp)

# 配置 PVS-Studio 静态分析目标
pvs_studio_add_target(TARGET example1.analyze ALL
                      OUTPUT FORMAT xml
                      ANALYZE ${PROJECT_NAME}
                      MODE GA:1,2
                      LOG "${CMAKE_BINARY_DIR}/pvs_analysis.xml")
