<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width,initial-scale=1.0">
  <link rel="icon" href="<%= BASE_URL %>favicon.ico">
  <title>脆弱性检测平台</title>
  <style>
    /* 加载动画 */
    #loading-wrapper {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: #fff;
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 9999;
    }

    .loading-spinner {
      width: 50px;
      height: 50px;
      border: 5px solid #f3f3f3;
      border-top: 5px solid #409EFF;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
  </style>
</head>
<body>
<noscript>
  <strong>很抱歉，脆弱性检测平台需要启用 JavaScript 才能正常工作。请启用 JavaScript 后重试。</strong>
</noscript>

<div id="app">
  <div id="loading-wrapper">
    <div class="loading-spinner"></div>
  </div>
</div>
<!-- built files will be auto injected -->

<script>
  // 应用加载完成后移除加载动画
  window.addEventListener('load', function() {
    setTimeout(function() {
      const loader = document.getElementById('loading-wrapper');
      if (loader) {
        loader.style.display = 'none';
      }
    }, 300);
  });
</script>
</body>
</html>
