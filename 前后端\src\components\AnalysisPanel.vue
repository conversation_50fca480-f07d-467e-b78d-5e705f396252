<template>
    <div class="analysis-panel">
        <el-card>
            <div slot="header">
                <span>漏洞分析</span>
            </div>

            <!-- 分析选项 -->
            <el-form :model="analysisOptions" label-width="120px">
                <el-form-item label="分析模式">
                    <el-radio-group v-model="analysisOptions.mode">
                        <el-radio label="full">完整分析</el-radio>
                        <el-radio label="quick">快速扫描</el-radio>
                        <!-- <el-radio label="custom">自定义</el-radio> -->
                    </el-radio-group>
                </el-form-item>

                <el-collapse v-if="analysisOptions.mode === 'custom'">
                    <el-collapse-item title="分析模块选择" name="modules">
                        <el-checkbox-group v-model="analysisOptions.modules">
                            <el-checkbox label="sast">静态安全扫描 (SAST)</el-checkbox>
                            <el-checkbox label="context">外部依赖解析</el-checkbox>
                            <el-checkbox label="vector">特征向量提取</el-checkbox>
                            <el-checkbox label="historical">历史漏洞匹配</el-checkbox>
                            <el-checkbox label="llm">LLM综合分析</el-checkbox>
                        </el-checkbox-group>
                    </el-collapse-item>
                </el-collapse>

                <el-form-item label="代码范围">
                    <el-radio-group v-model="analysisOptions.scope">
                        <el-radio label="file">整个文件</el-radio>
                        <el-radio label="selection" :disabled="!selectedCode">选中代码</el-radio>
                    </el-radio-group>
                </el-form-item>

                <el-form-item>
                    <el-button
                        type="primary"
                        @click="startAnalysis"
                        :loading="analyzing"
                        :disabled="!canAnalyze"
                    >
                        {{ analyzing ? '分析中...' : '开始分析' }}
                    </el-button>
                    <el-button v-if="analyzing" @click="cancelAnalysis">
                        取消分析
                    </el-button>
                </el-form-item>
            </el-form>

            <!-- 分析进度 -->
            <div v-if="analyzing" class="analysis-progress">
                <h4>分析进度</h4>
                <el-steps :active="currentStep" finish-status="success">
                    <el-step title="静态扫描" :description="stepDescriptions[0]"></el-step>
                    <el-step title="上下文解析" :description="stepDescriptions[1]"></el-step>
                    <el-step title="特征提取" :description="stepDescriptions[2]"></el-step>
                    <el-step title="历史匹配" :description="stepDescriptions[3]"></el-step>
                    <el-step title="综合分析" :description="stepDescriptions[4]"></el-step>
                </el-steps>

                <el-progress
                    :percentage="progressPercentage"
                    :status="progressStatus"
                    style="margin-top: 20px"
                ></el-progress>
            </div>

            <!-- 实时日志 -->
            <div v-if="logs.length > 0" class="analysis-logs">
                <h4>分析日志</h4>
                <div class="log-container">
                    <div
                        v-for="(log, index) in logs"
                        :key="index"
                        class="log-item"
                        :class="`log-${log.level}`"
                    >
                        <span class="log-time">{{ formatTime(log.time) }}</span>
                        <span class="log-message">{{ log.message }}</span>
                    </div>
                </div>
            </div>

            <!-- LLM 响应文件列表 -->
            <div v-if="llmResponseFiles.length > 0" class="response-files">
                <h4>
                    <i class="el-icon-folder-opened"></i>
                    LLM 响应文件
                </h4>
                <el-table :data="llmResponseFiles" size="small" max-height="200">
                    <el-table-column prop="fileName" label="文件名" show-overflow-tooltip />
                    <el-table-column prop="timestamp" label="时间" width="180">
                        <template slot-scope="scope">
                            {{ formatTime(scope.row.timestamp) }}
                        </template>
                    </el-table-column>
                    <el-table-column label="操作" width="100">
                        <template slot-scope="scope">
                            <el-button
                                size="mini"
                                type="text"
                                @click="loadResponseFile(scope.row)"
                            >
                                加载
                            </el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
        </el-card>
    </div>
</template>

<script>
import api from '@/api'

export default {
    name: 'AnalysisPanel',
    props: {
        file: {
            type: Object,
            required: true
        },
        fileContent: {
            type: String,
            default: ''
        },
        selectedCode: {
            type: Object,
            default: null
        }
    },
    data() {
        return {
            analysisOptions: {
                mode: 'full',
                modules: ['sast', 'context', 'vector', 'historical', 'llm'],
                scope: 'file'
            },
            analyzing: false,
            currentStep: 0,
            stepDescriptions: ['', '', '', '', ''],
            progressPercentage: 0,
            progressStatus: null,
            logs: [],
            analysisTask: null,
            llmResponseFiles: [],
            autoLoadLatest: true // 自动加载最新响应
        }
    },
    computed: {
        canAnalyze() {
            return this.file && this.fileContent && !this.analyzing
        },
        codeToAnalyze() {
            if (this.analysisOptions.scope === 'selection' && this.selectedCode) {
                return this.selectedCode.code
            }
            return this.fileContent
        }
    },
    mounted() {
        // 组件加载时检查是否有现有的 LLM 响应文件
        this.checkExistingResponses()
    },
    methods: {
        async checkExistingResponses() {
            try {
                const repository = this.$store.state.currentRepository
                if (!repository || !repository.extractPath) return

                // 调用 API 获取响应文件列表
                const response = await api.getLLMResponseFiles(
                    repository.extractPath,
                    this.file.name
                )

                if (response.success && response.data.files) {
                    this.llmResponseFiles = response.data.files.map(file => ({
                        fileName: file.name,
                        path: file.path,
                        timestamp: new Date(file.timestamp),
                        size: file.size
                    }))

                    // 如果设置了自动加载且有文件，加载最新的
                    if (this.autoLoadLatest && this.llmResponseFiles.length > 0) {
                        const latestFile = this.llmResponseFiles[0]
                        this.loadResponseFile(latestFile)
                        this.addLog(`自动加载最新的分析结果: ${latestFile.fileName}`, 'info')
                    }
                }
            } catch (error) {
                console.error('检查响应文件失败:', error)
            }
        },

        async loadResponseFile(responseFile) {
            try {
                this.addLog(`正在加载响应文件: ${responseFile.fileName}`, 'info')

                // 调用 API 读取响应文件内容
                const response = await api.loadLLMResponse(responseFile.path)

                if (response.success && response.data) {
                    // 解析响应数据并构建分析结果
                    const analysisResult = this.parseLoadedResponse(response.data, responseFile)
                    
                    // 触发分析完成事件
                    this.$emit('analysis-complete', analysisResult)
                    
                    this.addLog('成功加载历史分析结果', 'success')
                    this.$message.success('已加载历史分析结果')
                }
            } catch (error) {
                this.addLog('加载响应文件失败: ' + error.message, 'error')
                this.$message.error('加载失败: ' + error.message)
            }
        },

        parseLoadedResponse(responseData, responseFile) {
            const analysisResult = {
                fileId: this.file.id,
                fileName: this.file.name,
                filePath: this.file.path,
                startTime: responseFile.timestamp,
                endTime: responseFile.timestamp,
                vulnerabilities: [],
                dependencies: [],
                metrics: {},
                scope: 'file',
                analyzedCode: this.fileContent,
                isLoadedFromFile: true,
                responseFile: responseFile.fileName
            }

            // 解析漏洞数据
            if (responseData.parsed_vulnerabilities) {
                responseData.parsed_vulnerabilities.forEach((vuln, index) => {
                    const cweIds = vuln['源代码中确认存在漏洞的CWE_ID'] || []
                    const reasoning = vuln['推理过程'] || '无详细描述'
                    const lineInfo = vuln['在文件中的行数'] || ''

                    // 解析行号
                    let startLine = 0, endLine = 0
                    const lineMatch = lineInfo.match(/(\d+)/)
                    if (lineMatch) {
                        startLine = endLine = parseInt(lineMatch[1])
                    }

                    cweIds.forEach(cweId => {
                        analysisResult.vulnerabilities.push({
                            id: `loaded_${Date.now()}_${index}_${cweId}`,
                            type: `LLM确认的漏洞`,
                            severity: 'high',
                            startLine: startLine,
                            endLine: endLine,
                            message: reasoning,
                            code: cweId,
                            cwe: cweId.replace('CWE-', ''),
                            file: this.file.path,
                            tool: responseData.llm_model || 'Claude-3.5-Sonnet',
                            isLLMConfirmed: true,
                            reasoning: reasoning,
                            lineInfo: lineInfo
                        })
                    })
                })
            }

            // 设置 LLM 分析结果
            analysisResult.llmInsights = {
                summary: `从历史响应文件加载了 ${analysisResult.vulnerabilities.length} 个确认的漏洞`,
                riskLevel: analysisResult.vulnerabilities.length >= 3 ? 'high' : 
                          analysisResult.vulnerabilities.length >= 1 ? 'medium' : 'low',
                recommendations: [
                    '这是历史分析结果',
                    '如需最新分析，请重新运行分析',
                    '检查代码是否已经修复这些问题'
                ],
                responseFile: responseFile.path,
                loadedAt: new Date()
            }

            return analysisResult
        },

        async startAnalysis() {
            this.analyzing = true
            this.currentStep = 0
            this.progressPercentage = 0
            this.logs = []

            try {
                const modules = this.analysisOptions.mode === 'full'
                    ? ['sast', 'context', 'vector', 'historical', 'llm']
                    : this.analysisOptions.mode === 'quick'
                        ? ['sast']
                        : this.analysisOptions.modules

                const analysisResult = {
                    fileId: this.file.id,
                    fileName: this.file.name,
                    filePath: this.file.path,
                    startTime: new Date(),
                    vulnerabilities: [],
                    dependencies: [],
                    metrics: {},
                    scope: this.analysisOptions.scope,
                    analyzedCode: this.codeToAnalyze
                }

                for (let i = 0; i < modules.length; i++) {
                    const module = modules[i]
                    this.currentStep = i
                    this.progressPercentage = Math.round((i / modules.length) * 100)

                    await this.runAnalysisModule(module, analysisResult)

                    if (!this.analyzing) break
                }

                if (this.analyzing) {
                    this.progressPercentage = 100
                    this.progressStatus = 'success'
                    analysisResult.endTime = new Date()

                    this.$emit('analysis-complete', analysisResult)
                    this.addLog('分析完成', 'success')
                    this.$message.success('漏洞分析完成')

                    // 分析完成后，刷新响应文件列表
                    setTimeout(() => {
                        this.checkExistingResponses()
                    }, 1000)
                }
            } catch (error) {
                this.progressStatus = 'exception'
                this.addLog('分析失败：' + error.message, 'error')
                this.$message.error('分析失败：' + error.message)
            } finally {
                this.analyzing = false
            }
        },

        async runAnalysisModule(module, result) {
            const moduleNames = {
                sast: '静态安全扫描',
                context: '外部依赖解析',
                vector: '特征向量提取',
                historical: '历史漏洞匹配',
                llm: 'LLM综合分析'
            }

            this.stepDescriptions[this.currentStep] = '正在执行...'
            this.addLog(`开始${moduleNames[module]}`, 'info')

            try {
                switch (module) {
                    case 'sast':
                        await this.runStaticAnalysis(result)
                        break
                    case 'context':
                        await this.runDependencyAnalysis(result)
                        break
                    case 'vector':
                        await this.runFeatureExtraction(result)
                        break
                    case 'historical':
                        await this.runHistoricalMatching(result)
                        break
                    case 'llm':
                        await this.runLLMAnalysis(result)
                        break
                }
                this.stepDescriptions[this.currentStep] = '完成'
            } catch (error) {
                this.stepDescriptions[this.currentStep] = '失败'
                throw error
            }
        },

        async runStaticAnalysis(result) {
            this.addLog('正在调用 SAST 分析...', 'info')

            try {
                const repository = this.$store.state.currentRepository
                if (!repository || !repository.extractPath) {
                    throw new Error('找不到项目解压路径')
                }

                const response = await api.SASTanalyze(
                    repository.extractPath,
                    this.file.path,
                    'sast'
                )

                if (response.success && response.data) {
                    const analysisData = response.data

                    if (analysisData.vulnerabilities && Array.isArray(analysisData.vulnerabilities)) {
                        result.vulnerabilities = analysisData.vulnerabilities.map((vuln, index) => ({
                            id: `sast_${Date.now()}_${index}`,
                            type: vuln.code || vuln.type,
                            severity: vuln.severity || 'medium',
                            startLine: vuln.line || 0,
                            endLine: vuln.line || 0,
                            message: vuln.message || 'SAST检测到潜在问题',
                            code: vuln.code || 'SAST',
                            cwe: vuln.cwe || 'N/A',
                            file: this.file.path,
                            tool: 'PVS-Studio'
                        }))
                    }

                    this.addLog(
                        `SAST 分析完成，发现 ${result.vulnerabilities.length} 个问题`,
                        result.vulnerabilities.length > 0 ? 'warning' : 'success'
                    )
                } else {
                    result.vulnerabilities = []
                    this.addLog('SAST 分析完成', 'warning')
                }
            } catch (error) {
                this.addLog('SAST 分析出错：' + error.message, 'error')
                result.vulnerabilities = []
            }
        },

        async runDependencyAnalysis(result) {
            await new Promise(resolve => setTimeout(resolve, 1500))

            const dependencies = []

            if (this.file.name === 'package.json') {
                try {
                    const packageData = JSON.parse(this.codeToAnalyze)
                    const deps = { ...packageData.dependencies, ...packageData.devDependencies }

                    Object.entries(deps).forEach(([name, version]) => {
                        if (Math.random() > 0.8) {
                            dependencies.push({
                                name,
                                version,
                                vulnerability: {
                                    severity: Math.random() > 0.5 ? 'high' : 'medium',
                                    description: `${name} ${version} 存在已知安全漏洞`,
                                    cve: `CVE-2024-${Math.floor(Math.random() * 10000)}`
                                }
                            })
                        }
                    })
                } catch (e) {
                    this.addLog('package.json 解析失败', 'warning')
                }
            }

            result.dependencies = dependencies
            this.addLog(`依赖分析完成`,
                dependencies.length > 0 ? 'warning' : 'success')
        },

        async runFeatureExtraction(result) {
            this.addLog('正在进行特征提取和相似代码匹配...', 'info')

            try {
                const response = await api.extractFeatures(
                    this.codeToAnalyze,
                    this.$store.state.currentRepository?.extractPath
                )

                if (response.success && response.data?.features) {
                    const features = response.data.features
                    const similarCodes = features.similar_codes || []
                    
                    const metrics = {
                        totalLines: this.codeToAnalyze.split('\n').length,
                        complexity: this.calculateComplexity(this.codeToAnalyze),
                        similarCodeMatches: similarCodes.length,
                        topSimilarityScore: similarCodes.length > 0 ? 
                            (1 - similarCodes[0].distance).toFixed(3) : 0
                    }

                    const vulnerableMatches = similarCodes.filter(match => match.IF_VUL === true)
                    
                    if (vulnerableMatches.length > 0) {
                        this.addLog(
                            `发现 ${vulnerableMatches.length} 个相似的漏洞代码模式`,
                            'warning'
                        )

                        vulnerableMatches.forEach((match, index) => {
                            result.vulnerabilities.push({
                                id: `similarity_${Date.now()}_${index}`,
                                type: `相似度分析`,
                                severity: 'info',
                                startLine: 0,
                                endLine: 0,
                                message: `CWE-${match.CWE_ID} | 相似度: ${(1 - match.distance).toFixed(3)}`,
                                code: match.file_name,
                                cwe: match.CWE_ID,
                                file: this.file.path,
                                tool: 'CodeBERT',
                                similarCode: match.code,
                                distance: match.distance,
                                isSimilarityAnalysis: true
                            })
                        })
                    }

                    result.metrics = {
                        ...result.metrics,
                        ...metrics,
                        codeFeatures: {
                            preprocessedLength: features.preprocessed_code?.length || 0,
                            hasSecurityPatterns: this.extractSecurityFeatures(this.codeToAnalyze),
                            similarCodeAnalysis: {
                                totalMatches: similarCodes.length,
                                vulnerableMatches: vulnerableMatches.length,
                                cweDistribution: this.analyzeCWEDistribution(vulnerableMatches)
                            }
                        }
                    }

                    result.similarCodes = similarCodes

                    this.addLog('特征提取和相似代码匹配完成', 'success')
                } else {
                    this.addLog('特征提取完成', 'warning')
                }
            } catch (error) {
                this.addLog('特征提取失败：' + error.message, 'error')
            }
        },

        analyzeCWEDistribution(vulnerableMatches) {
            const distribution = {}
            vulnerableMatches.forEach(match => {
                const cweId = match.CWE_ID
                distribution[cweId] = (distribution[cweId] || 0) + 1
            })
            return distribution
        },

        async runHistoricalMatching(result) {
            await new Promise(resolve => setTimeout(resolve, 1500))

            const historicalMatches = []

            if (result.vulnerabilities.length > 0) {
                result.vulnerabilities.forEach(vuln => {
                    if (Math.random() > 0.7) {
                        historicalMatches.push({
                            vulnerability: vuln.type,
                            similarCases: Math.floor(Math.random() * 10) + 1,
                            averageSeverity: vuln.severity,
                            commonFixes: ['输入验证', '使用安全函数', '权限检查']
                        })
                    }
                })
            }

            result.historicalMatches = historicalMatches
            this.addLog(`历史匹配完成`, 'info')
        },

        async runLLMAnalysis(result) {
            this.addLog('正在进行 LLM 综合分析...', 'info')
            
            try {
                const repository = this.$store.state.currentRepository
                if (!repository || !repository.extractPath) {
                    throw new Error('找不到项目解压路径')
                }

                const response = await api.analyzeLLM(
                    repository.extractPath,
                    this.file.path,
                    'prompt'
                )

                if (response.success && response.data) {
                    const llmData = response.data

                    const llmInsights = {
                        summary: '',
                        riskLevel: 'low',
                        recommendations: [],
                        estimatedFixTime: '需要根据具体问题评估',
                        promptPath: llmData.prompt_path,
                        responsePath: llmData.llm_response_path,
                        rawResponse: llmData.llm_response
                    }

                    if (llmData.detected_vulnerabilities && llmData.detected_vulnerabilities.length > 0) {
                        llmData.detected_vulnerabilities.forEach((vuln, index) => {
                            const cweIds = vuln['源代码中确认存在漏洞的CWE_ID'] || []
                            const reasoning = vuln['推理过程'] || '无详细描述'
                            const lineInfo = vuln['在文件中的行数'] || ''

                            // 解析行号
                            let startLine = 0, endLine = 0
                            const lineMatch = lineInfo.match(/(\d+)/)
                            if (lineMatch) {
                                startLine = endLine = parseInt(lineMatch[1])
                            }

                            cweIds.forEach(cweId => {
                                result.vulnerabilities.push({
                                    id: `llm_${Date.now()}_${index}_${cweId}`,
                                    type: `LLM确认的漏洞`,
                                    severity: 'high',
                                    startLine: startLine,
                                    endLine: endLine,
                                    message: reasoning,
                                    code: cweId,
                                    cwe: cweId.replace('CWE-', ''),
                                    file: this.file.path,
                                    tool: 'Claude-3.5-Sonnet',
                                    isLLMConfirmed: true,
                                    reasoning: reasoning,
                                    lineInfo: lineInfo
                                })
                            })
                        })

                        const vulnCount = llmData.vulnerability_count || 0
                        if (vulnCount >= 3) {
                            llmInsights.riskLevel = 'high'
                        } else if (vulnCount >= 1) {
                            llmInsights.riskLevel = 'medium'
                        } else {
                            llmInsights.riskLevel = 'low'
                        }

                        llmInsights.summary = `LLM 分析确认了 ${vulnCount} 个漏洞，涉及 ${llmData.detected_cwes.join(', ')}`
                        
                        llmInsights.recommendations = [
                            '立即审查 LLM 确认的漏洞',
                            '根据 CWE 指南实施修复方案',
                            '进行代码审查验证修复效果',
                            '添加相应的安全测试用例'
                        ]

                        this.addLog(
                            `LLM 分析完成，确认 ${vulnCount} 个漏洞`,
                            'warning'
                        )
                    } else {
                        llmInsights.summary = 'LLM 分析未发现确认的安全漏洞'
                        llmInsights.recommendations = [
                            '继续保持良好的编码实践',
                            '定期进行安全审查',
                            '关注依赖库的安全更新'
                        ]
                        
                        this.addLog('LLM 分析完成，未发现确认的漏洞', 'success')
                    }

                    result.llmInsights = llmInsights

                    if (llmData.prompt_path) {
                        this.addLog(`Prompt 已保存至: ${llmData.prompt_path}`, 'info')
                    }
                    
                    if (llmData.llm_response_path) {
                        this.addLog(`LLM 响应已保存至: ${llmData.llm_response_path}`, 'info')
                    }

                } else {
                    result.llmInsights = {
                        summary: 'LLM 分析失败',
                        riskLevel: 'unknown',
                        recommendations: ['请稍后重试或联系技术支持'],
                        error: response.message || 'Unknown error'
                    }
                    
                    this.addLog('LLM 分析失败：' + (response.message || 'Unknown error'), 'error')
                }
            } catch (error) {
                this.addLog('LLM 分析出错：' + error.message, 'error')
                
                result.llmInsights = {
                    summary: 'LLM 分析出错',
                    riskLevel: 'unknown',
                    recommendations: ['检查网络连接', '确认 API 密钥配置正确', '查看错误日志'],
                    error: error.message
                }
            }
        },

        calculateComplexity(code) {
            const conditions = (code.match(/if|else|for|while|switch|case/g) || []).length
            return conditions + 1
        },

        extractSecurityFeatures(code) {
            return {
                hasInputValidation: code.includes('validate') || code.includes('sanitize'),
                hasErrorHandling: code.includes('try') || code.includes('catch'),
                hasAuthentication: code.includes('auth') || code.includes('token'),
                hasEncryption: code.includes('encrypt') || code.includes('crypto')
            }
        },

        cancelAnalysis() {
            this.analyzing = false
            this.progressStatus = 'warning'
            this.addLog('用户取消分析', 'warning')
            this.$message.warning('已取消分析')
        },

        addLog(message, level = 'info') {
            this.logs.push({
                time: new Date(),
                message,
                level
            })

            this.$nextTick(() => {
                const container = this.$el.querySelector('.log-container')
                if (container) {
                    container.scrollTop = container.scrollHeight
                }
            })
        },

        formatTime(date) {
            return new Date(date).toLocaleTimeString('zh-CN')
        }
    }
}
</script>

<style scoped>
.analysis-panel {
    height: 100%;
}

.analysis-progress {
    margin-top: 30px;
    padding: 20px;
    background-color: #f5f7fa;
    border-radius: 4px;
}

.analysis-progress h4 {
    margin-bottom: 20px;
    color: #303133;
}

.analysis-logs {
    margin-top: 30px;
    padding: 20px;
    background-color: #f5f7fa;
    border-radius: 4px;
}

.analysis-logs h4 {
    margin-bottom: 10px;
    color: #303133;
}

.log-container {
    max-height: 200px;
    overflow-y: auto;
    background-color: #fff;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    padding: 10px;
}

.log-item {
    margin-bottom: 5px;
    font-size: 12px;
    font-family: monospace;
}

.log-time {
    margin-right: 10px;
    color: #909399;
}

.log-info {
    color: #606266;
}

.log-warning {
    color: #e6a23c;
}

.log-error {
    color: #f56c6c;
}

.log-success {
    color: #67c23a;
}

.response-files {
    margin-top: 30px;
    padding: 20px;
    background-color: #f5f7fa;
    border-radius: 4px;
}

.response-files h4 {
    margin-bottom: 15px;
    color: #303133;
    display: flex;
    align-items: center;
}

.response-files h4 i {
    margin-right: 8px;
    font-size: 18px;
    color: #409eff;
}

.response-files .el-table {
    background-color: transparent;
}

.response-files .el-button--text {
    color: #409eff;
}

.response-files .el-button--text:hover {
    color: #66b1ff;
}
</style>

