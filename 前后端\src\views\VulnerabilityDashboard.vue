<template>
    <div class="vulnerability-dashboard">
        <!-- 页面标题 -->
        <div class="dashboard-header">
            <h2>
                <i class="el-icon-data-analysis"></i>
                漏洞分析仪表板
            </h2>
            <div class="header-actions">
                <el-select 
                    v-model="selectedRepository" 
                    placeholder="选择仓库"
                    @change="handleRepositoryChange"
                    style="width: 250px"
                >
                    <el-option
                        v-for="repo in repositories"
                        :key="repo.name"
                        :label="repo.name"
                        :value="repo.name"
                    >
                        <span style="float: left">{{ repo.name }}</span>
                        <span style="float: right; color: #8492a6; font-size: 13px">
                            {{ repo.fileCount }} 个文件
                        </span>
                    </el-option>
                </el-select>
                <el-button 
                    type="primary" 
                    icon="el-icon-refresh"
                    @click="loadResponseFiles"
                    :loading="loading"
                >
                    刷新数据
                </el-button>
                <el-button 
                    type="success" 
                    icon="el-icon-download"
                    @click="exportData"
                    :disabled="tableData.length === 0"
                >
                    导出报告
                </el-button>
            </div>
        </div>

        <!-- 统计卡片 -->
        <el-row :gutter="20" class="stat-cards">
            <el-col :xs="24" :sm="12" :md="6">
                <el-card shadow="hover">
                    <div class="stat-item">
                        <div class="stat-icon" style="background-color: #409EFF">
                            <i class="el-icon-document"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-value">{{ statistics.totalFiles }}</div>
                            <div class="stat-label">分析文件总数</div>
                        </div>
                    </div>
                </el-card>
            </el-col>
            <el-col :xs="24" :sm="12" :md="6">
                <el-card shadow="hover">
                    <div class="stat-item">
                        <div class="stat-icon" style="background-color: #F56C6C">
                            <i class="el-icon-warning"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-value">{{ statistics.totalVulnerabilities }}</div>
                            <div class="stat-label">漏洞总数</div>
                        </div>
                    </div>
                </el-card>
            </el-col>
            <el-col :xs="24" :sm="12" :md="6">
                <el-card shadow="hover">
                    <div class="stat-item">
                        <div class="stat-icon" style="background-color: #E6A23C">
                            <i class="el-icon-s-data"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-value">{{ statistics.avgVulnerabilitiesPerFile }}</div>
                            <div class="stat-label">平均每文件漏洞数</div>
                        </div>
                    </div>
                </el-card>
            </el-col>
            <el-col :xs="24" :sm="12" :md="6">
                <el-card shadow="hover">
                    <div class="stat-item">
                        <div class="stat-icon" style="background-color: #67C23A">
                            <i class="el-icon-check"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-value">{{ statistics.safeFiles }}</div>
                            <div class="stat-label">安全文件数</div>
                        </div>
                    </div>
                </el-card>
            </el-col>
        </el-row>

        <!-- 图表区域 -->
        <el-row :gutter="20" style="margin-top: 20px">
            <!-- 漏洞分布柱状图 -->
            <el-col :span="12">
                <el-card>
                    <div slot="header" class="clearfix">
                        <span>文件漏洞分布 TOP 10</span>
                        <el-radio-group 
                            v-model="chartType" 
                            size="small" 
                            style="float: right"
                        >
                            <el-radio-button label="bar">柱状图</el-radio-button>
                            <el-radio-button label="horizontal">横向图</el-radio-button>
                        </el-radio-group>
                    </div>
                    <div class="chart-container" ref="vulnerabilityChart"></div>
                </el-card>
            </el-col>

            <!-- CWE分布饼图 -->
            <el-col :span="12">
                <el-card>
                    <div slot="header" class="clearfix">
                        <span>CWE类型分布</span>
                    </div>
                    <div class="chart-container" ref="cweChart"></div>
                </el-card>
            </el-col>
        </el-row>

        <!-- 仓库漏洞热点图 -->
        <el-row :gutter="20" style="margin-top: 20px">
            <el-col :span="24">
                <el-card>
                    <div slot="header" class="clearfix">
                        <span>仓库漏洞热点分布</span>
                        <span style="float: right; font-size: 14px; color: #909399">点击矩形块可深入查看子目录</span>
                    </div>
                    <div class="chart-container-wide" ref="heatmapChart"></div>
                </el-card>
            </el-col>
        </el-row>

        <!-- 详细数据表格 -->
        <el-card style="margin-top: 20px">
            <div slot="header" class="clearfix">
                <span>详细漏洞数据</span>
                <el-input
                    v-model="searchKeyword"
                    placeholder="搜索文件名或CWE编号"
                    style="width: 300px; float: right"
                    prefix-icon="el-icon-search"
                    clearable
                />
            </div>
            <el-table
                :data="filteredTableData"
                style="width: 100%"
                :height="400"
                stripe
                @row-click="handleRowClick"
            >
                <el-table-column
                    prop="fileName"
                    label="文件名"
                    min-width="200"
                    show-overflow-tooltip
                    sortable
                >
                    <template slot-scope="scope">
                        <span class="file-link">{{ scope.row.fileName }}</span>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="vulnerabilityCount"
                    label="漏洞数量"
                    width="120"
                    align="center"
                    sortable
                >
                    <template slot-scope="scope">
                        <el-tag 
                            :type="getCountTagType(scope.row.vulnerabilityCount)"
                            size="small"
                        >
                            {{ scope.row.vulnerabilityCount }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="highSeverity"
                    label="高危"
                    width="80"
                    align="center"
                >
                    <template slot-scope="scope">
                        <span v-if="scope.row.highSeverity > 0" class="severity-high">
                            {{ scope.row.highSeverity }}
                        </span>
                        <span v-else>-</span>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="mediumSeverity"
                    label="中危"
                    width="80"
                    align="center"
                >
                    <template slot-scope="scope">
                        <span v-if="scope.row.mediumSeverity > 0" class="severity-medium">
                            {{ scope.row.mediumSeverity }}
                        </span>
                        <span v-else>-</span>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="lowSeverity"
                    label="低危"
                    width="80"
                    align="center"
                >
                    <template slot-scope="scope">
                        <span v-if="scope.row.lowSeverity > 0" class="severity-low">
                            {{ scope.row.lowSeverity }}
                        </span>
                        <span v-else>-</span>
                    </template>
                </el-table-column>
                <el-table-column
                    label="主要CWE类型"
                    min-width="200"
                >
                    <template slot-scope="scope">
                        <el-tooltip
                            v-for="(cwe, index) in scope.row.topCWETypes"
                            :key="index"
                            :content="`${cwe.cwe}: ${cwe.count} 个`"
                            placement="top"
                        >
                            <el-tag
                                size="mini"
                                style="margin-right: 5px"
                                :type="getCWETagType(index)"
                            >
                                {{ cwe.cwe }}
                            </el-tag>
                        </el-tooltip>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="analyzedAt"
                    label="分析时间"
                    width="180"
                    sortable
                >
                    <template slot-scope="scope">
                        {{ formatTime(scope.row.analyzedAt) }}
                    </template>
                </el-table-column>
                <el-table-column
                    label="操作"
                    width="150"
                    fixed="right"
                >
                    <template slot-scope="scope">
                        <el-button
                            size="mini"
                            type="primary"
                            @click.stop="viewDetails(scope.row)"
                        >
                            查看详情
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
        </el-card>

        <!-- 详情对话框 -->
        <el-dialog
            :title="`漏洞详情 - ${currentFile ? currentFile.fileName : ''}`"
            :visible.sync="detailsVisible"
            width="80%"
            top="5vh"
        >
            <div v-if="currentFile">
                <el-descriptions :column="2" border style="margin-bottom: 20px">
                    <el-descriptions-item label="文件路径">
                        {{ currentFile.filePath }}
                    </el-descriptions-item>
                    <el-descriptions-item label="漏洞总数">
                        <el-tag type="danger">{{ currentFile.vulnerabilityCount }}</el-tag>
                    </el-descriptions-item>
                    <el-descriptions-item label="分析时间">
                        {{ formatTime(currentFile.analyzedAt) }}
                    </el-descriptions-item>
                    <el-descriptions-item label="使用模型">
                        {{ currentFile.llmModel || 'Unknown' }}
                    </el-descriptions-item>
                </el-descriptions>

                <el-table
                    :data="currentFile.vulnerabilities"
                    style="width: 100%"
                    max-height="400"
                >
                    <el-table-column
                        type="expand"
                    >
                        <template slot-scope="props">
                            <div class="vulnerability-detail">
                                <p><strong>详细描述：</strong></p>
                                <p>{{ props.row.reasoning }}</p>
                                <p><strong>代码位置：</strong></p>
                                <p>{{ props.row.lineInfo }}</p>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="cwe"
                        label="CWE编号"
                        width="150"
                    />
                    <el-table-column
                        prop="severity"
                        label="严重程度"
                        width="100"
                    >
                        <template slot-scope="scope">
                            <el-tag :type="getSeverityType(scope.row.severity)">
                                {{ scope.row.severity }}
                            </el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="lineInfo"
                        label="位置"
                        width="150"
                    />
                    <el-table-column
                        prop="reasoning"
                        label="描述"
                        show-overflow-tooltip
                    />
                </el-table>
            </div>
        </el-dialog>

        <!-- 加载状态 -->
        <div v-if="loading" class="loading-overlay">
            <i class="el-icon-loading"></i>
            <p>正在加载数据...</p>
        </div>
    </div>
</template>

<script>
import * as echarts from 'echarts'
import api from '@/api'
import { color } from 'echarts'

export default {
    name: 'VulnerabilityDashboard',
    data() {
        return {
            repositories: [],
            selectedRepository: null,
            responseFiles: [],
            loading: false,
            searchKeyword: '',
            detailsVisible: false,
            currentFile: null,
            chartType: 'bar',
            
            // 图表实例
            vulnerabilityChart: null,
            cweChart: null,
            heatmapChart: null,
            
            // 统计信息
            statistics: {
                totalFiles: 0,
                totalVulnerabilities: 0,
                avgVulnerabilitiesPerFile: '0',
                safeFiles: 0
            },
            
            // 表格数据
            tableData: [],
            
            // 仓库文件结构数据
            repositoryStructure: []
        }
    },
    
    computed: {
        filteredTableData() {
            if (!this.searchKeyword) {
                return this.tableData
            }
            
            const keyword = this.searchKeyword.toLowerCase()
            return this.tableData.filter(item => {
                return item.fileName.toLowerCase().includes(keyword) ||
                       item.topCWETypes.some(cwe => 
                           cwe.cwe.toLowerCase().includes(keyword)
                       )
            })
        }
    },
    
    watch: {
        chartType() {
            this.updateVulnerabilityChart()
        }
    },
    
    async mounted() {
        await this.loadRepositories()
        this.initCharts()
        
        // 监听窗口大小变化
        window.addEventListener('resize', this.handleResize)
    },
    
    beforeDestroy() {
        // 销毁图表实例
        if (this.vulnerabilityChart) this.vulnerabilityChart.dispose()
        if (this.cweChart) this.cweChart.dispose()
        if (this.heatmapChart) this.heatmapChart.dispose()
        
        // 移除事件监听
        window.removeEventListener('resize', this.handleResize)
    },
    
    methods: {
        // CWE严重程度映射
        getCWESeverity(cwe) {
            // 根据CWE编号判断严重程度
            const highSeverityCWEs = ['CWE-78', 'CWE-89', 'CWE-79', 'CWE-434', 'CWE-611', 'CWE-22', 'CWE-352', 'CWE-287']
            const mediumSeverityCWEs = ['CWE-120', 'CWE-125', 'CWE-787', 'CWE-416', 'CWE-476', 'CWE-190', 'CWE-200', 'CWE-209']
            
            if (highSeverityCWEs.includes(cwe)) return 'high'
            if (mediumSeverityCWEs.includes(cwe)) return 'medium'
            return 'low'
        },
        
        // 处理窗口大小变化
        handleResize() {
            if (this.vulnerabilityChart) this.vulnerabilityChart.resize()
            if (this.cweChart) this.cweChart.resize()
            if (this.heatmapChart) this.heatmapChart.resize()
        },
        
        // 加载仓库列表
        async loadRepositories() {
            try {
                const response = await api.getRepositories()
                if (response.success) {
                    this.repositories = response.data
                    
                    // 自动选择第一个仓库或从 localStorage 恢复
                    const lastSelected = localStorage.getItem('lastSelectedRepository')
                    if (lastSelected && this.repositories.find(r => r.name === lastSelected)) {
                        this.selectedRepository = lastSelected
                    } else if (this.repositories.length > 0) {
                        this.selectedRepository = this.repositories[0].name
                    }
                    
                    if (this.selectedRepository) {
                        await this.loadResponseFiles()
                    }
                }
            } catch (error) {
                this.$message.error('加载仓库列表失败: ' + error.message)
            }
        },
        
        // 处理仓库切换
        async handleRepositoryChange(value) {
            localStorage.setItem('lastSelectedRepository', value)
            await this.loadResponseFiles()
        },
        
        // 加载响应文件
        async loadResponseFiles() {
            if (!this.selectedRepository) return
            
            this.loading = true
            try {
                const repo = this.repositories.find(r => r.name === this.selectedRepository)
                if (!repo) return
                
                // 保存仓库结构数据
                this.repositoryStructure = this.buildFileStructure(repo.files)
                
                // 获取所有文件的 LLM 响应
                const allResponses = []
                const processedFiles = new Set()
                
                // 遍历所有文件
                const files = this.getAllFiles(repo.files)
                
                for (const file of files) {
                    if (file.type !== 'file') continue
                    
                    try {
                        // 获取该文件的响应文件列表
                        const responseResult = await api.getLLMResponseFiles(
                            repo.extractPath, 
                            file.name
                        )
                        
                        if (responseResult.success && responseResult.data.files.length > 0) {
                            // 使用最新的响应文件
                            const latestFile = responseResult.data.files[0]
                            
                            // 避免重复处理
                            if (processedFiles.has(latestFile.path)) continue
                            processedFiles.add(latestFile.path)
                            
                            // 加载响应内容
                            const contentResult = await api.loadLLMResponse(latestFile.path)
                            
                            if (contentResult.success && contentResult.data) {
                                allResponses.push({
                                    fileName: file.name,
                                    filePath: file.path,
                                    responseData: contentResult.data,
                                    timestamp: latestFile.timestamp
                                })
                            }
                        }
                    } catch (error) {
                        console.error(`加载文件 ${file.name} 的响应失败:`, error)
                    }
                }
                
                // 处理数据
                this.processResponseData(allResponses)
                
                if (allResponses.length === 0) {
                    this.$message.info('该仓库暂无分析数据')
                }
                
            } catch (error) {
                this.$message.error('加载数据失败: ' + error.message)
            } finally {
                this.loading = false
            }
        },
        
        // 构建文件结构（用于热点图）
        buildFileStructure(items, path = '') {
            const result = []
            for (const item of items) {
                const currentPath = path ? `${path}/${item.name}` : item.name
                if (item.type === 'directory' && item.children) {
                    const children = this.buildFileStructure(item.children, currentPath)
                    if (children.length > 0) {
                        result.push({
                            name: item.name,
                            path: currentPath,
                            children: children
                        })
                    }
                } else if (item.type === 'file') {
                    result.push({
                        name: item.name,
                        path: currentPath,
                        value: 0  // 将在处理响应数据时更新
                    })
                }
            }
            return result
        },
        
        // 获取所有文件（递归）
        getAllFiles(items, files = []) {
            for (const item of items) {
                if (item.type === 'file') {
                    files.push(item)
                } else if (item.children) {
                    this.getAllFiles(item.children, files)
                }
            }
            return files
        },
        
        // 处理响应数据
        processResponseData(responses) {
            // 重置数据
            this.tableData = []
            const cweDistribution = {}
            const severityCount = { high: 0, medium: 0, low: 0 }
            let totalVulnerabilities = 0
            
            // 创建文件路径到漏洞数的映射
            const fileVulnerabilityMap = {}
            
            // 处理每个文件的响应
            responses.forEach(response => {
                const responseData = response.responseData.data || response.responseData
                const vulnerabilities = responseData.parsed_vulnerabilities || []
                const vulnerabilityCount = responseData.vulnerability_count || vulnerabilities.length
                totalVulnerabilities += vulnerabilityCount
                
                // 更新文件漏洞映射
                fileVulnerabilityMap[response.filePath] = vulnerabilityCount
                
                // 统计严重程度和CWE
                const fileSeverity = { high: 0, medium: 0, low: 0 }
                const fileCWEs = {}
                
                // 处理每个漏洞
                const processedVulnerabilities = []
                vulnerabilities.forEach(vuln => {
                    // 提取CWE编号
                    let cweList = []
                    if (vuln['源代码中确认存在漏洞的CWE_ID']) {
                        cweList = vuln['源代码中确认存在漏洞的CWE_ID']
                    } else if (vuln.cwe) {
                        cweList = [vuln.cwe]
                    }
                    
                    // 处理每个CWE
                    cweList.forEach(cwe => {
                        // 统计CWE
                        fileCWEs[cwe] = (fileCWEs[cwe] || 0) + 1
                        cweDistribution[cwe] = (cweDistribution[cwe] || 0) + 1
                        
                        // 获取严重程度
                        const severity = this.getCWESeverity(cwe)
                        fileSeverity[severity]++
                        severityCount[severity]++
                        
                        // 添加到处理后的漏洞列表
                        processedVulnerabilities.push({
                            cwe: cwe,
                            severity: severity,
                            reasoning: vuln['推理过程'] || vuln.reasoning || '',
                            lineInfo: vuln['在文件中的行数'] || vuln.line_info || '',
                            type: cwe,  // 兼容性
                            message: vuln['推理过程'] || vuln.message || ''
                        })
                    })
                })
                
                // 获取前3个最多的CWE类型
                const topCWEs = Object.entries(fileCWEs)
                    .map(([cwe, count]) => ({ cwe, count }))
                    .sort((a, b) => b.count - a.count)
                    .slice(0, 3)
                
                // 添加到表格数据
                this.tableData.push({
                    fileName: response.fileName,
                    filePath: response.filePath || responseData.file_path,
                    vulnerabilityCount: vulnerabilityCount,
                    highSeverity: fileSeverity.high,
                    mediumSeverity: fileSeverity.medium,
                    lowSeverity: fileSeverity.low,
                    topCWETypes: topCWEs,
                    topVulnerabilityTypes: topCWEs, // 保持兼容性
                    vulnerabilities: processedVulnerabilities,
                    analyzedAt: response.timestamp || responseData.timestamp,
                    llmModel: responseData.llm_model
                })
            })
            
            // 更新热点图数据
            this.updateHeatmapData(fileVulnerabilityMap)
            
            // 更新统计信息
            this.statistics = {
                totalFiles: responses.length,
                totalVulnerabilities: totalVulnerabilities,
                avgVulnerabilitiesPerFile: responses.length > 0 ? 
                    (totalVulnerabilities / responses.length).toFixed(2) : '0',
                safeFiles: this.tableData.filter(f => f.vulnerabilityCount === 0).length
            }
            
            // 更新图表
            this.updateCharts(cweDistribution, severityCount)
        },
        
        // 更新热点图数据
        updateHeatmapData(fileVulnerabilityMap) {
            const updateNode = (node) => {
                if (node.children) {
                    node.children.forEach(child => updateNode(child))
                    // 计算目录的总漏洞数
                    node.value = node.children.reduce((sum, child) => sum + (child.value || 0), 0)
                } else {
                    // 更新文件的漏洞数
                    node.value = fileVulnerabilityMap[node.path] || 0
                }
            }
            
            this.repositoryStructure.forEach(node => updateNode(node))
        },
        
        // 初始化图表
        initCharts() {
            // 初始化漏洞分布图
            const chartDom1 = this.$refs.vulnerabilityChart
            if (chartDom1) {
                this.vulnerabilityChart = echarts.init(chartDom1)
            }
            
            // 初始化CWE分布饼图
            const chartDom2 = this.$refs.cweChart
            if (chartDom2) {
                this.cweChart = echarts.init(chartDom2)
            }
            
            // 初始化热点图
            const chartDom3 = this.$refs.heatmapChart
            if (chartDom3) {
                this.heatmapChart = echarts.init(chartDom3)
            }
        },
        
        // 更新图表
        updateCharts(cweDistribution, severityCount) {
            // 更新漏洞分布图
            this.updateVulnerabilityChart()
            
            // 更新CWE分布饼图
            this.updateCWEChart(cweDistribution)
            
            // 更新热点图
            this.updateHeatmap()
        },
        
        // 更新漏洞分布图
        updateVulnerabilityChart() {
            if (!this.vulnerabilityChart) return
            
            // 获取前10个漏洞最多的文件
            const topFiles = [...this.tableData]
                .sort((a, b) => b.vulnerabilityCount - a.vulnerabilityCount)
                .slice(0, 10)
            
            const option = {
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    }
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                xAxis: {
                    type: this.chartType  === 'horizontal' ? 'value' : 'category',
                    data: this.chartType === 'horizontal' ? undefined : topFiles.map(f => {
                        const name = f.fileName
                        return name.length > 20 ? name.substring(0, 20) + '...' : name
                    }),
                    axisLabel: {
                        interval: 0,
                        rotate: this.chartType === 'horizontal' ? 0 : 45
                    }
                },
                yAxis: {
                    type: this.chartType === 'horizontal' ? 'category' : 'value',
                    data: this.chartType === 'horizontal' ? topFiles.map(f => {
                        const name = f.fileName
                        return name.length > 20 ? name.substring(0, 20) + '...' : name
                    }) : undefined
                },
                series: [{
                    name: '漏洞数量',
                    type: 'bar',
                    data: topFiles.map(f => f.vulnerabilityCount),
                    itemStyle: {
                        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                            { offset: 0, color: '#F56C6C' },
                            { offset: 1, color: '#E6A23C' }
                        ])
                    }
                }]
            }
            
            this.vulnerabilityChart.setOption(option)
        },
        
        // 更新CWE分布饼图
        updateCWEChart(cweDistribution) {
            if (!this.cweChart) return
            
            // 获取前10个CWE类型
            const sortedCWEs = Object.entries(cweDistribution)
                .sort((a, b) => b[1] - a[1])
                .slice(0, 10)
            
            const option = {
                tooltip: {
                    trigger: 'item',
                    formatter: '{a} <br/>{b}: {c} ({d}%)'
                },
                legend: {
                    type: 'scroll',
                    orient: 'vertical',
                    right: 10,
                    top: 20,
                    bottom: 20,
                    data: sortedCWEs.map(([cwe]) => cwe)
                },
                series: [{
                    name: 'CWE分布',
                    type: 'pie',
                    radius: ['40%', '70%'],
                    center: ['40%', '50%'],
                    avoidLabelOverlap: false,
                    itemStyle: {
                        borderRadius: 10,
                        borderColor: '#fff',
                        borderWidth: 2
                    },
                    label: {
                        show: false,
                        position: 'center'
                    },
                    emphasis: {
                        label: {
                            show: true,
                            fontSize: '20',
                            fontWeight: 'bold'
                        }
                    },
                    labelLine: {
                        show: false
                    },
                    data: sortedCWEs.map(([cwe, count], index) => ({
                        value: count,
                        name: cwe,
                        itemStyle: {
                            color: this.getColorPalette()[index % 10]
                        }
                    }))
                }]
            }
            
            this.cweChart.setOption(option)
        },
        
        // 更新热点图
        updateHeatmap() {
            if (!this.heatmapChart) return
            
            const option = {
                tooltip: {
                    formatter: function(info) {
                        const value = info.value || 0
                        const treePathInfo = info.treePathInfo
                        const treePath = []
                        for (let i = 1; i < treePathInfo.length; i++) {
                            treePath.push(treePathInfo[i].name)
                        }
                        return [
                            '<div class="tooltip-title">' + 
                            echarts.format.encodeHTML(treePath.join('/')) + 
                            '</div>',
                            '漏洞数量: ' + value
                        ].join('')
                    }
                },
                series: [{
                    type: 'treemap',
                    name: '仓库漏洞分布',
                    leafDepth: 2,
                    levels: [
                        {
                            itemStyle: {
                                borderColor: '#fff',
                                borderWidth: 4,
                                gapWidth: 4
                            }
                        },
                        {
                            colorSaturation: [0.3, 0.7],
                            itemStyle: {
                                borderColorSaturation: 0.7,
                                gapWidth: 2,
                                borderWidth: 2
                            }
                        },
                        {
                            colorSaturation: [0.3, 0.6],
                            itemStyle: {
                                borderColorSaturation: 0.6,
                                gapWidth: 1
                            }
                        }
                    ],
                    data: this.repositoryStructure,
                    label: {
                        show: true,
                        formatter: '{b}',
                        fontSize: 12
                    },
                    itemStyle: {
                        gapWidth: 1,
                        borderColor: '#fff'
                    },
                    breadcrumb: {
                        show: true,
                        left: 'center',
                        top: 'top',
                        itemStyle: {
                            textStyle: {
                                fontSize: 14
                            }
                        }
                    },
                   // 在 updateHeatmap 方法中修改
                    
      
                
                    colorMappingBy: 'value'
                }],
                visualMap: {
                type: 'piecewise',
                    pieces: [
                        { min: 0, max: 0, label: '安全', color: '#67C23A' },      // 绿色表示安全
                        { min: 1, max: 3, label: '低危(1-3个)', color: '#E6A23C' },  // 黄色表示低危
                        { min: 4, max: 6, label: '中危(4-6个)', color: '#F56C6C' },  // 橙色表示中危
                        { min: 7, label: '高危(>7个)', color: '#FF0000' }           // 红色表示高危
                    ],
                    orient: 'vertical',
                    left: 'right',
                    top: 'middle',
                    textStyle: {
                        fontSize: 12
                    },
                    calculable: true
                },

            }
            
            this.heatmapChart.setOption(option)
        },
        
        // 获取所有节点的值
        getAllNodeValues(nodes, values = []) {
            nodes.forEach(node => {
                if (node.value !== undefined && node.value !== null) {
                    values.push(node.value)
                }
                if (node.children) {
                    this.getAllNodeValues(node.children, values)
                }
            })
            return values
        },
        
        // 获取颜色调色板
        getColorPalette() {
            return [
                '#F56C6C', '#E6A23C', '#67C23A', '#409EFF', '#909399',
                '#FF7F50', '#87CEEB', '#FFD700', '#98FB98', '#DDA0DD'
            ]
        },
        
        // 获取漏洞数量标签类型
        getCountTagType(count) {
            if (count === 0) return 'success'
            if (count <= 5) return 'warning'
            return 'danger'
        },
        
        // 获取CWE标签类型
        getCWETagType(index) {
            const types = ['danger', 'warning', 'info']
            return types[index] || 'info'
        },
        
        // 获取严重程度标签类型
        getSeverityType(severity) {
            const severityLower = severity?.toLowerCase()
            if (severityLower === 'high') return 'danger'
            if (severityLower === 'medium') return 'warning'
            return 'info'
        },
        
        // 格式化时间
        formatTime(date) {
            if (!date) return ''
            // 处理不同格式的时间戳
            if (typeof date === 'string' && date.match(/^\d{8}_\d{6}$/)) {
                // 格式: 20250607_223454
                const year = date.substring(0, 4)
                const month = date.substring(4, 6)
                const day = date.substring(6, 8)
                const hour = date.substring(9, 11)
                const minute = date.substring(11, 13)
                const second = date.substring(13, 15)
                return `${year}-${month}-${day} ${hour}:${minute}:${second}`
            }
            return new Date(date).toLocaleString('zh-CN')
        },
        
        // 处理行点击
        handleRowClick(row) {
            this.viewDetails(row)
        },
        
        // 查看详情
        viewDetails(file) {
            this.currentFile = file
            this.detailsVisible = true
        },
        
        // 导出数据
        exportData() {
            const exportData = {
                repository: this.selectedRepository,
                exportTime: new Date().toISOString(),
                statistics: this.statistics,
                files: this.tableData,
                cweDistribution: this.getCWEDistributionData(),
                heatmapData: this.repositoryStructure,
                severityDistribution: this.getSeverityDistribution()
            }
            
            const dataStr = JSON.stringify(exportData, null, 2)
            const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr)
            
            const timestamp = new Date().toISOString().replace(/[:.-]/g, '')
            const exportFileName = `vulnerability_dashboard_${this.selectedRepository}_${timestamp}.json`
            
            const linkElement = document.createElement('a')
            linkElement.setAttribute('href', dataUri)
            linkElement.setAttribute('download', exportFileName)
            linkElement.click()
            
            // 同时生成 CSV
            this.exportCSV()
            
            // 生成可视化报告
            this.exportVisualReport()
            
            this.$message.success('报告已导出')
        },
        
        // 导出 CSV
        exportCSV() {
            const headers = ['文件名', '文件路径', '漏洞总数', '高危', '中危', '低危', '主要CWE', '分析时间']
            const rows = this.tableData.map(row => [
                row.fileName,
                row.filePath,
                row.vulnerabilityCount,
                row.highSeverity,
                row.mediumSeverity,
                row.lowSeverity,
                row.topCWETypes.map(cwe => cwe.cwe).join('; '),
                this.formatTime(row.analyzedAt)
            ])
            
            // 转换为 CSV
            const csvContent = [
                headers.join(','),
                ...rows.map(row => row.map(cell => `"${cell}"`).join(','))
            ].join('\n')
            
            const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' })
            const link = document.createElement('a')
            const url = URL.createObjectURL(blob)
            
            const timestamp = new Date().toISOString().replace(/[:.-]/g, '')
            link.setAttribute('href', url)
            link.setAttribute('download', `vulnerability_data_${this.selectedRepository}_${timestamp}.csv`)
            link.style.visibility = 'hidden'
            
            document.body.appendChild(link)
            link.click()
            document.body.removeChild(link)
        },
        
        // 导出可视化报告
        exportVisualReport() {
            // 生成图表截图
            Promise.all([
                this.vulnerabilityChart?.getDataURL(),
                this.cweChart?.getDataURL(),
                this.heatmapChart?.getDataURL()
            ]).then(([vulnChartImg, cweChartImg, heatmapImg]) => {
                const htmlContent = this.generateHTMLReport(vulnChartImg, cweChartImg, heatmapImg)
                const blob = new Blob([htmlContent], { type: 'text/html;charset=utf-8' })
                const link = document.createElement('a')
                const url = URL.createObjectURL(blob)
                
                const timestamp = new Date().toISOString().replace(/[:.-]/g, '')
                link.setAttribute('href', url)
                link.setAttribute('download', `vulnerability_report_${this.selectedRepository}_${timestamp}.html`)
                link.click()
            })
        },
        
        // 生成HTML报告
        generateHTMLReport(vulnChartImg, cweChartImg, heatmapImg) {
            return `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>漏洞分析报告 - ${this.selectedRepository}</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        h1, h2 { color: #333; }
        .stat-grid { display: grid; grid-template-columns: repeat(4, 1fr); gap: 20px; margin: 20px 0; }
        .stat-card { background: #f5f7fa; padding: 20px; border-radius: 8px; text-align: center; }
        .stat-value { font-size: 32px; font-weight: bold; color: #409EFF; }
        .stat-label { color: #666; margin-top: 10px; }
        .chart-img { max-width: 100%; margin: 20px 0; }
        table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f5f7fa; }
        .severity-high { color: #F56C6C; font-weight: bold; }
        .severity-medium { color: #E6A23C; font-weight: bold; }
        .severity-low { color: #909399; }
    </style>
</head>
<body>
    <h1>漏洞分析报告</h1>
    <p>仓库: ${this.selectedRepository}</p>
    <p>生成时间: ${new Date().toLocaleString('zh-CN')}</p>
    
    <h2>统计概览</h2>
    <div class="stat-grid">
        <div class="stat-card">
            <div class="stat-value">${this.statistics.totalFiles}</div>
            <div class="stat-label">分析文件总数</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">${this.statistics.totalVulnerabilities}</div>
            <div class="stat-label">漏洞总数</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">${this.statistics.avgVulnerabilitiesPerFile}</div>
            <div class="stat-label">平均每文件漏洞数</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">${this.statistics.safeFiles}</div>
            <div class="stat-label">安全文件数</div>
        </div>
    </div>
    
    <h2>文件漏洞分布</h2>
    <img src="${vulnChartImg}" class="chart-img" alt="文件漏洞分布图">
    
    <h2>CWE类型分布</h2>
    <img src="${cweChartImg}" class="chart-img" alt="CWE类型分布图">
    
    <h2>仓库漏洞热点图</h2>
    <img src="${heatmapImg}" class="chart-img" alt="仓库漏洞热点图">
    
    <h2>详细数据</h2>
    <table>
        <thead>
            <tr>
                <th>文件名</th>
                <th>漏洞总数</th>
                <th>高危</th>
                <th>中危</th>
                <th>低危</th>
                <th>主要CWE</th>
            </tr>
        </thead>
        <tbody>
            ${this.tableData.map(row => `
            <tr>
                <td>${row.fileName}</td>
                <td>${row.vulnerabilityCount}</td>
                <td class="severity-high">${row.highSeverity || '-'}</td>
                <td class="severity-medium">${row.mediumSeverity || '-'}</td>
                <td class="severity-low">${row.lowSeverity || '-'}</td>
                <td>${row.topCWETypes.map(cwe => cwe.cwe).join(', ')}</td>
            </tr>
            `).join('')}
        </tbody>
    </table>
</body>
</html>
            `
        },
        
        // 获取CWE分布数据
        getCWEDistributionData() {
            const cwes = {}
            this.tableData.forEach(file => {
                file.topCWETypes.forEach(cwe => {
                    cwes[cwe.cwe] = (cwes[cwe.cwe] || 0) + cwe.count
                })
            })
            return cwes
        },
        
        // 获取严重程度分布
        getSeverityDistribution() {
            return {
                high: this.tableData.reduce((sum, f) => sum + f.highSeverity, 0),
                medium: this.tableData.reduce((sum, f) => sum + f.mediumSeverity, 0),
                low: this.tableData.reduce((sum, f) => sum + f.lowSeverity, 0)
            }
        }
    }
}
</script>

<style scoped>
.vulnerability-dashboard {
    padding: 20px;
    background-color: #f5f7fa;
    min-height: calc(100vh - 60px);
}

.dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
}

.dashboard-header h2 {
    margin: 0;
    display: flex;
    align-items: center;
    gap: 10px;
    color: #303133;
}

.header-actions {
    display: flex;
    gap: 10px;
}

/* 统计卡片样式 */
.stat-cards {
    margin-bottom: 20px;
}

.stat-item {
    display: flex;
    align-items: center;
    padding: 20px;
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 16px;
    flex-shrink: 0;
}

.stat-icon i {
    font-size: 28px;
    color: white;
}

.stat-content {
    flex: 1;
}

.stat-value {
    font-size: 32px;
    font-weight: 600;
    color: #303133;
    line-height: 1.2;
}

.stat-label {
    font-size: 14px;
    color: #909399;
    margin-top: 4px;
}

/* 图表容器样式 */
.chart-container {
    height: 350px;
    padding: 10px;
}

.chart-container-wide {
    height: 400px;
    padding: 10px;
}

/* 表格样式 */
.file-link {
    color: #409EFF;
    cursor: pointer;
}

.file-link:hover {
    text-decoration: underline;
}

.severity-high {
    color: #F56C6C;
    font-weight: bold;
}

.severity-medium {
    color: #E6A23C;
    font-weight: bold;
}

.severity-low {
    color: #909399;
}

/* 详情对话框样式 */
.vulnerability-detail {
    padding: 20px;
    background-color: #f5f7fa;
    border-radius: 4px;
}

.vulnerability-detail p {
    margin: 10px 0;
    line-height: 1.6;
}

/* 加载状态 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.9);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 999;
}

.loading-overlay i {
    font-size: 48px;
    color: #409EFF;
    animation: rotate 1s linear infinite;
}

.loading-overlay p {
    margin-top: 20px;
    font-size: 16px;
    color: #606266;
}

@keyframes rotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .dashboard-header {
        flex-direction: column;
        gap: 15px;
    }
    
    .header-actions {
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .stat-item {
        flex-direction: column;
        text-align: center;
    }
    
    .stat-icon {
        margin-right: 0;
        margin-bottom: 12px;
    }
    
    .chart-container {
        height: 300px;
    }
    
    .chart-container-wide {
        height: 350px;
    }
}

/* Element UI 覆盖样式 */
::v-deep .el-card {
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
}

::v-deep .el-table {
    font-size: 14px;
}

::v-deep .el-dialog__header {
    background-color: #f5f7fa;
    padding: 20px;
    border-bottom: 1px solid #e4e7ed;
}

::v-deep .el-dialog__body {
    padding: 20px;
}

::v-deep .el-tag {
    border-radius: 4px;
}

/* Tooltip 样式 */
.tooltip-title {
    font-weight: bold;
    margin-bottom: 4px;
}
</style>
