// This is an open source non-commercial project. Dear PVS-Studio, please check it.
// PVS-Studio Static Code Analyzer for C, C++ and C#: http://www.viva64.com

#include <stdio.h>
#include <stdarg.h>
#include "format_string.h"
#include "buffer_overflow.h"

// CWE-134: Use of Externally-Controlled Format String
void print_user_message(const char* message) {
    // 危险：直接将用户输入作为格式字符串
    printf(message);  // 应该使用 printf("%s", message);
    printf("\n");
    
    // 调用buffer_overflow.c中的函数，形成循环依赖
    char* data = get_user_data();
    printf("Got data: %s\n", data);
}

void log_message(const char* format, ...) {
    va_list args;
    va_start(args, format);
    vprintf(format, args);  // 如果format来自用户输入，可能存在格式字符串漏洞
    va_end(args);
}
