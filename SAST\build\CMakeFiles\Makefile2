# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /root/preprocess/SAST

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /root/preprocess/SAST/build

#=============================================================================
# Directory level rules for the build root directory

# The main recursive "all" target.
all: CMakeFiles/pvs-studio-cmake-example-1.dir/all
all: CMakeFiles/example1.analyze.dir/all
.PHONY : all

# The main recursive "preinstall" target.
preinstall:
.PHONY : preinstall

# The main recursive "clean" target.
clean: CMakeFiles/pvs-studio-cmake-example-1.dir/clean
clean: CMakeFiles/example1.analyze-pvs_analysis.xml-log.dir/clean
clean: CMakeFiles/example1.analyze.dir/clean
.PHONY : clean

#=============================================================================
# Target rules for target CMakeFiles/pvs-studio-cmake-example-1.dir

# All Build rule for target.
CMakeFiles/pvs-studio-cmake-example-1.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/pvs-studio-cmake-example-1.dir/build.make CMakeFiles/pvs-studio-cmake-example-1.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/pvs-studio-cmake-example-1.dir/build.make CMakeFiles/pvs-studio-cmake-example-1.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/root/preprocess/SAST/build/CMakeFiles --progress-num=3,4 "Built target pvs-studio-cmake-example-1"
.PHONY : CMakeFiles/pvs-studio-cmake-example-1.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/pvs-studio-cmake-example-1.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /root/preprocess/SAST/build/CMakeFiles 2
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/pvs-studio-cmake-example-1.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /root/preprocess/SAST/build/CMakeFiles 0
.PHONY : CMakeFiles/pvs-studio-cmake-example-1.dir/rule

# Convenience name for target.
pvs-studio-cmake-example-1: CMakeFiles/pvs-studio-cmake-example-1.dir/rule
.PHONY : pvs-studio-cmake-example-1

# clean rule for target.
CMakeFiles/pvs-studio-cmake-example-1.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/pvs-studio-cmake-example-1.dir/build.make CMakeFiles/pvs-studio-cmake-example-1.dir/clean
.PHONY : CMakeFiles/pvs-studio-cmake-example-1.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/example1.analyze-pvs_analysis.xml-log.dir

# All Build rule for target.
CMakeFiles/example1.analyze-pvs_analysis.xml-log.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/example1.analyze-pvs_analysis.xml-log.dir/build.make CMakeFiles/example1.analyze-pvs_analysis.xml-log.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/example1.analyze-pvs_analysis.xml-log.dir/build.make CMakeFiles/example1.analyze-pvs_analysis.xml-log.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/root/preprocess/SAST/build/CMakeFiles --progress-num=1,2 "Built target example1.analyze-pvs_analysis.xml-log"
.PHONY : CMakeFiles/example1.analyze-pvs_analysis.xml-log.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/example1.analyze-pvs_analysis.xml-log.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /root/preprocess/SAST/build/CMakeFiles 2
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/example1.analyze-pvs_analysis.xml-log.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /root/preprocess/SAST/build/CMakeFiles 0
.PHONY : CMakeFiles/example1.analyze-pvs_analysis.xml-log.dir/rule

# Convenience name for target.
example1.analyze-pvs_analysis.xml-log: CMakeFiles/example1.analyze-pvs_analysis.xml-log.dir/rule
.PHONY : example1.analyze-pvs_analysis.xml-log

# clean rule for target.
CMakeFiles/example1.analyze-pvs_analysis.xml-log.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/example1.analyze-pvs_analysis.xml-log.dir/build.make CMakeFiles/example1.analyze-pvs_analysis.xml-log.dir/clean
.PHONY : CMakeFiles/example1.analyze-pvs_analysis.xml-log.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/example1.analyze.dir

# All Build rule for target.
CMakeFiles/example1.analyze.dir/all: CMakeFiles/pvs-studio-cmake-example-1.dir/all
CMakeFiles/example1.analyze.dir/all: CMakeFiles/example1.analyze-pvs_analysis.xml-log.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/example1.analyze.dir/build.make CMakeFiles/example1.analyze.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/example1.analyze.dir/build.make CMakeFiles/example1.analyze.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/root/preprocess/SAST/build/CMakeFiles --progress-num= "Built target example1.analyze"
.PHONY : CMakeFiles/example1.analyze.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/example1.analyze.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /root/preprocess/SAST/build/CMakeFiles 4
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/example1.analyze.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /root/preprocess/SAST/build/CMakeFiles 0
.PHONY : CMakeFiles/example1.analyze.dir/rule

# Convenience name for target.
example1.analyze: CMakeFiles/example1.analyze.dir/rule
.PHONY : example1.analyze

# clean rule for target.
CMakeFiles/example1.analyze.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/example1.analyze.dir/build.make CMakeFiles/example1.analyze.dir/clean
.PHONY : CMakeFiles/example1.analyze.dir/clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

