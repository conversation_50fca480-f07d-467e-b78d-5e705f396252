{"cells": [{"cell_type": "code", "execution_count": null, "id": "ac0c7253-aa29-4865-a443-c590f2132c32", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "a22c60c5-9052-4bc6-8077-7b4fd3fb9118", "metadata": {}, "outputs": [], "source": ["import zipfile\n", "\n", "def unzip_file(zip_file_path, output_directory):\n", "    \"\"\"\n", "    解压整个 ZIP 文件到指定目录\n", "    :param zip_file_path: ZIP 文件的路径\n", "    :param output_directory: 解压缩的目标文件夹路径\n", "    \"\"\"\n", "    with zipfile.ZipFile(zip_file_path, 'r') as zip_ref:\n", "        # 解压所有文件到目标目录\n", "        zip_ref.extractall(output_directory)\n", "        print(f\"解压完成！所有文件解压至 {output_directory}\")\n", "\n", "\n", "zip_file_path = \"2017-10-01-juliet-test-suite-for-c-cplusplus-v1-3.zip\"  # ZIP 文件路径\n", "output_directory = \"./output\"  # 目标解压路径\n", "unzip_file(zip_file_path, output_directory)\n"]}, {"cell_type": "code", "execution_count": 4, "id": "85501030-88bf-4cee-8f68-67b24fe8bdc0", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["JSON output successfully written to analyzed_files.json\n"]}], "source": ["import os\n", "import json\n", "import re\n", "\n", "def remove_main_blocks(content):\n", "    \"\"\"\n", "    Remove blocks enclosed by #ifdef INCLUDEMAIN and #endif (including these lines themselves).\n", "    \n", "    Args:\n", "        content (str): The content of the file as a string.\n", "    \n", "    Returns:\n", "        str: The content after removing any #ifdef INCLUDEMAIN blocks.\n", "    \"\"\"\n", "    lines = content.splitlines()  # Split content into lines\n", "    cleaned_lines = []\n", "    inside_includemain = False\n", "    nesting_level = 0  # To keep track of nested #ifdef INCLUDEMAIN\n", "\n", "    for line in lines:\n", "        # Check for #ifdef INCLUDEMAIN\n", "        if \"#ifdef INCLUDEMAIN\" in line:\n", "            inside_includemain = True\n", "            nesting_level += 1\n", "            continue  # Skip this line\n", "\n", "        # Check for nested preprocessor directives\n", "        if inside_includemain and \"#ifndef\" in line:\n", "            nesting_level += 1\n", "        if inside_includemain and \"#endif\" in line:\n", "            nesting_level -= 1\n", "            if nesting_level == 0:\n", "                inside_includemain = False\n", "            continue  # Skip this line\n", "\n", "        # Append lines outside of INCLUDEMAIN block\n", "        if not inside_includemain:\n", "            cleaned_lines.append(line)\n", "\n", "    return \"\\n\".join(cleaned_lines)\n", "\n", "\n", "def extract_code(content, start_tag, end_tag):\n", "    \"\"\"\n", "    Extract code blocks between start_tag and the matching end_tag,\n", "    accounting for nested #ifdef/#ifndef sections.\n", "    \"\"\"\n", "    code_blocks = []\n", "    lines = content.splitlines()  # Split content into lines\n", "    stack = []  # Stack to handle nesting\n", "    current_block = []  # To store the lines of the current block\n", "\n", "    for line in lines:\n", "        # Check for the start tag\n", "        if start_tag in line:\n", "            if len(stack) == 0:  # Top-level start tag found\n", "                current_block = []  # Start a new block\n", "            stack.append(start_tag)  # Push onto the stack\n", "\n", "        # Always append lines when inside a block\n", "        if len(stack) > 0:\n", "            current_block.append(line)\n", "\n", "        # Check for the end tag\n", "        if end_tag in line and len(stack) > 0:\n", "            stack.pop()  # Pop the stack\n", "            if len(stack) == 0:  # Top-level block finished\n", "                \n", "                code_blocks.append(\"\\n\".join(current_block))  # Store the block\n", "                current_block = []  # Reset for next block\n", "    \n", "\n", "    return code_blocks\n", "\n", "def clean_code_block(code_block):\n", "    \"\"\"\n", "    Clean the code block by removing comments and extra lines.\n", "    \"\"\"\n", "    # Remove single-line comments (// ...)\n", "    code_block = re.sub(r\"//.*\", \"\", code_block)\n", "    # Remove multi-line comments (/* ... */)\n", "    code_block = re.sub(r\"/\\*[\\s\\S]*?\\*/\", \"\", code_block)\n", "    \n", "    # Strip extra whitespace lines\n", "    code_block = \"\\n\".join([line.strip() for line in code_block.splitlines() if line.strip()])\n", "    return code_block\n", "\n", "def remove_print_statements(code_block):\n", "    \"\"\"\n", "    Remove all print-related function calls from the code block.\n", "    Examples:\n", "        print(\"Hello, <PERSON>\");\n", "        printLine(\"This is a test\");\n", "    \"\"\"\n", "    # Regex to match print-like function calls\n", "    print_patterns = [\n", "        r\"\\bprint\\w*\\s*\\([^;]*\\);\",  # Matches print or printLine calls like \"print(...);\" or \"printLine(...);\"\n", "    ]\n", "\n", "    for pattern in print_patterns:\n", "        code_block = re.sub(pattern, \"\", code_block)\n", "\n", "    return code_block\n", "\n", "def is_include_main_block(code_block):\n", "    \"\"\"\n", "    Check if the given code block contains \"#ifdef INCLUDEMAIN\" or similar main program logic.\n", "    \"\"\"\n", "    main_function_patterns = [\n", "        r\"#ifdef\\s+INCLUDEMAIN\",       # Check for #ifdef INCLUDEMAIN\n", "        r\"int\\s+main\\s*\\(\",           # Check for main function declaration\n", "        r\"seed randomness\"            # Check for seed randomness comments\n", "    ]\n", "    for pattern in main_function_patterns:\n", "        if re.search(pattern, code_block, re.IGNORECASE):\n", "            return True\n", "    return False\n", "\n", "def remove_preprocessor_directives(code_block, start_macro):\n", "    \"\"\"\n", "    Remove the #ifndef <start_macro> if it's the first line \n", "    and the corresponding #endif if it's the last line.\n", "    \n", "    Args:\n", "        code_block (str): The input code block as a string.\n", "        start_macro (str): The macro to check (e.g., \"OMITBAD\" or \"OMITGOOD\").\n", "    \n", "    Returns:\n", "        str: The modified code block with directives removed (if present).\n", "    \"\"\"\n", "    lines = code_block.splitlines()\n", "    \n", "    # Check the first line\n", "    if lines and lines[0].strip() == f\"#ifndef {start_macro}\":\n", "        lines.pop(0)  # Remove the first line\n", "\n", "    # Check the last line\n", "    if lines and lines[-1].strip() == \"#endif\":\n", "        lines.pop(-1)  # Remove the last line\n", "\n", "    # Rejoin the remaining lines into a single string\n", "    return \"\\n\".join(lines)\n", "\n", "\n", "def anonymize_cwe_functions(code_block):\n", "    \"\"\"\n", "    Anonymize all functions with 'CWE' in their name.\n", "    \"\"\"\n", "    placeholder_mapping = {}  # Map to store function name and its placeholder\n", "    function_pattern = r\"\\b(CWE\\w+)\\b\"  # Match functions or identifiers starting with CWE\n", "\n", "    def replace_function_name(match):\n", "        \"\"\"\n", "        Replace matched CWE function with a consistent placeholder.\n", "        \"\"\"\n", "        function_name = match.group(1)\n", "        if function_name not in placeholder_mapping:\n", "            placeholder_mapping[function_name] = f\"func_{len(placeholder_mapping) + 1}\"\n", "        return placeholder_mapping[function_name]\n", "\n", "    # Replace all CWE-prefixed functions with placeholders\n", "    anonymized_code = re.sub(function_pattern, replace_function_name, code_block)\n", "    return anonymized_code\n", "\n", "def extract_cwe_id_from_filename(file_name):\n", "    \"\"\"\n", "    Extract CWE ID from the filename (e.g., CWE176_XXXX.cpp -> CWE-176).\n", "    \"\"\"\n", "    match = re.search(r\"CWE(\\d+)\", file_name)\n", "    if match:\n", "        return f\"CWE-{match.group(1)}\"\n", "    return \"Unknown\"\n", "\n", "def analyze_file(file_path):\n", "    \"\"\"\n", "    Analyze a given file for CWE ID, IF_VUL, and code blocks.\n", "    \"\"\"\n", "    results = []\n", "    file_name = os.path.basename(file_path)\n", "    cwe_id = extract_cwe_id_from_filename(file_name)  # Extract CWE ID from the file name\n", "\n", "    with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:\n", "        content = f.read()\n", "\n", "        # Extract Description from comments\n", "        description_match = re.search(r\"@description\\s*([\\s\\S]*?)\\n\", content)\n", "        description = description_match.group(1).strip() if description_match else \"No description available.\"\n", "        \n", "        cleaned_content = remove_main_blocks(content)\n", "\n", "        # Extract \"BAD\" and \"GOOD\" code blocks\n", "        bad_code_blocks = extract_code(cleaned_content, r\"#ifndef OMITBAD\", r\"#endif\")\n", "        good_code_blocks = extract_code(cleaned_content, r\"#ifndef OMITGOOD\", r\"#endif\")\n", "\n", "        # Populate BAD entries\n", "        for bad_code in bad_code_blocks:\n", "            bad_code = clean_code_block(bad_code)\n", "            bad_code = anonymize_cwe_functions(bad_code)\n", "            # bad_code = remove_print_statements(bad_code)\n", "            bad_code = remove_preprocessor_directives(bad_code,\"OMITBAD\")\n", "            results.append({\n", "                \"file_name\": file_name,\n", "                \"CWE_ID\": cwe_id,\n", "                \"IF_VUL\": True,\n", "                \"Description\": description,\n", "                \"code\": bad_code\n", "            })\n", "\n", "        # Populate GOOD entries\n", "        for good_code in good_code_blocks:\n", "            good_code = clean_code_block(good_code)\n", "            good_code = anonymize_cwe_functions(good_code)\n", "            # good_code = remove_print_statements(good_code)\n", "            good_code = remove_preprocessor_directives(good_code,\"OMITGOOD\")\n", "            results.append({\n", "                \"file_name\": file_name,\n", "                \"CWE_ID\": cwe_id,\n", "                \"IF_VUL\": <PERSON><PERSON><PERSON>,\n", "                \"Description\": description,\n", "                \"code\": good_code\n", "            })\n", "\n", "    return results\n", "\n", "def process_directory(directory):\n", "    \"\"\"\n", "    Traverse the directory to find .c and .cpp files and analyze them.\n", "    \"\"\"\n", "    files_data = []\n", "    for root, _, files in os.walk(directory):\n", "        for file in files:\n", "            if file.endswith(\".c\") or file.endswith(\".cpp\"):\n", "                file_path = os.path.join(root, file)\n", "                file_data = analyze_file(file_path)\n", "                files_data.extend(file_data)  # Add `BAD` and `GOOD` entries separately\n", "    return files_data\n", "\n", "def main(input_directory, output_json):\n", "    \"\"\"\n", "    Main script execution.\n", "    \"\"\"\n", "    all_files_data = process_directory(input_directory)\n", "    with open(output_json, 'w', encoding='utf-8') as f:\n", "        json.dump(all_files_data, f, indent=4, ensure_ascii=False)\n", "    print(f\"JSON output successfully written to {output_json}\")\n", "\n", "if __name__ == \"__main__\":\n", "    # Define your input directory and output JSON file\n", "    input_directory = \"./output/C/testcases\"  # Replace with your directory containing .c/.cpp files\n", "    output_json = \"analyzed_files.json\"\n", "    \n", "    main(input_directory, output_json)\n"]}, {"cell_type": "code", "execution_count": null, "id": "302bbe45-e9f0-4f5f-b6e0-584be7141a66", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[\n", "{\n", "\"file_name\": \"CWE789_Uncontrolled_Mem_Alloc__malloc_char_rand_32.c\",\n", "\"CWE_ID\": \"CWE-789\",\n", "\"IF_VUL\": true,\n", "\"Description\": \"* CWE: 789 Uncontrolled Memory Allocation\",\n", "\"code\": \"void func_1()\\n{\\nsize_t data;\\nsize_t *dataPtr1 = &data;\\nsize_t *dataPtr2 = &data;\\ndata = 0;\\n{\\nsize_t data = *dataPtr1;\\ndata = rand();\\n*dataPtr1 = data;\\n}\\n{\\nsize_t data = *dataPtr2;\\n{\\nchar * myString;\\nif (data > strlen(HELLO_STRING))\\n{\\nmyString = (char *)malloc(data*sizeof(char));\\nif (myString == NULL) {exit(-1);}\\nstrcpy(myString, HELLO_STRING);\\n\\nfree(myString);\\n}\\nelse\\n{\\n\\n}\\n}\\n}\\n}\"\n", "},\n", "{\n", "\"file_name\": \"CWE789_Uncontrolled_Mem_Alloc__malloc_char_rand_32.c\",\n", "\"CWE_ID\": \"CWE-789\",\n", "\"IF_VUL\": false,\n", "\"Description\": \"* CWE: 789 Uncontrolled Memory Allocation\",\n", "\"code\": \"static void goodG2B()\\n{\\nsize_t data;\\nsize_t *dataPtr1 = &data;\\nsize_t *dataPtr2 = &data;\\ndata = 0;\\n{\\nsize_t data = *dataPtr1;\\ndata = 20;\\n*dataPtr1 = data;\\n}\\n{\\nsize_t data = *dataPtr2;\\n{\\nchar * myString;\\nif (data > strlen(HELLO_STRING))\\n{\\nmyString = (char *)malloc(data*sizeof(char));\\nif (myString == NULL) {exit(-1);}\\nstrcpy(myString, HELLO_STRING);\\n\\nfree(myString);\\n}\\nelse\\n{\\n\\n}\\n}\\n}\\n}\\nstatic void goodB2G()\\n{\\nsize_t data;\\nsize_t *dataPtr1 = &data;\\nsize_t *dataPtr2 = &data;\\ndata = 0;\\n{\\nsize_t data = *dataPtr1;\\ndata = rand();\\n*dataPtr1 = data;\\n}\\n{\\nsize_t data = *dataPtr2;\\n{\\nchar * myString;\\nif (data > strlen(HELLO_STRING) && data < 100)\\n{\\nmyString = (char *)malloc(data*sizeof(char));\\nif (myString == NULL) {exit(-1);}\\nstrcpy(myString, HELLO_STRING);\\n\\nfree(myString);\\n}\\nelse\\n{\\n\\n}\\n}\\n}\\n}\\nvoid func_1()\\n{\\ngoodG2B();\\ngoodB2G();\\n}\"\n", "},\n", "{\n", "\"file_name\": \"CWE789_Uncontrolled_Mem_Alloc__malloc_wchar_t_rand_67b.c\",\n", "\"CWE_ID\": \"CWE-789\",\n", "\"IF_VUL\": true,\n", "\"Description\": \"* CWE: 789 Uncontrolled Memory Allocation\",\n", "\"code\": \"void func_1(func_2 myStruct)\\n{\\nsize_t data = myStruct.structFirst;\\n{\\nwchar_t * myString;\\nif (data > wcslen(HELLO_STRING))\\n{\\nmyString = (wchar_t *)malloc(data*sizeof(wchar_t));\\nif (myString == NULL) {exit(-1);}\\nwcscpy(myString, HELLO_STRING);\\n\\nfree(myString);\\n}\\nelse\\n{\\n\\n}\\n}\\n}\"\n", "},\n", "{\n", "\"file_name\": \"CWE789_Uncontrolled_Mem_Alloc__malloc_wchar_t_rand_67b.c\",\n", "\"CWE_ID\": \"CWE-789\",\n", "\"IF_VUL\": false,\n", "\"Description\": \"* CWE: 789 Uncontrolled Memory Allocation\",\n", "\"code\": \"void func_1(func_2 myStruct)\\n{\\nsize_t data = myStruct.structFirst;\\n{\\nwchar_t * myString;\\nif (data > wcslen(HELLO_STRING))\\n{\\nmyString = (wchar_t *)malloc(data*sizeof(wchar_t));\\nif (myString == NULL) {exit(-1);}\\nwcscpy(myString, HELLO_STRING);\\n\\nfree(myString);\\n}\\nelse\\n{\\n\\n}\\n}\\n}\\nvoid func_3(func_2 myStruct)\\n{\\nsize_t data = myStruct.structFirst;\\n{\\nwchar_t * myString;\\nif (data > wcslen(HELLO_STRING) && data < 100)\\n{\\nmyString = (wchar_t *)malloc(data*sizeof(wchar_t));\\nif (myString == NULL) {exit(-1);}\\nwcscpy(myString, HELLO_STRING);\\n\\nfree(myString);\\n}\\nelse\\n{\\n\\n}\\n}\\n}\"\n", "},\n", "{\n", "\"file_name\": \"CWE789_Uncontrolled_Mem_Alloc__malloc_char_connect_socket_03.c\",\n", "\"CWE_ID\": \"CWE-789\",\n", "\"IF_VUL\": true,\n", "\"Description\": \"* CWE: 789 Uncontrolled Memory Allocation\",\n", "\"code\": \"void func_1()\\n{\\nsize_t data;\\ndata = 0;\\nif(5==5)\\n{\\n{\\n#ifdef _WIN32\\nWSADATA wsaData;\\nint wsaDataInit = 0;\"\n", "},\n", "{\n", "\"file_name\": \"CWE789_Uncontrolled_Mem_Alloc__malloc_char_connect_socket_03.c\",\n", "\"CWE_ID\": \"CWE-789\",\n", "\"IF_VUL\": false,\n", "\"Description\": \"* CWE: 789 Uncontrolled Memory Allocation\",\n", "\"code\": \"static void goodB2G1()\\n{\\nsize_t data;\\ndata = 0;\\nif(5==5)\\n{\\n{\\n#ifdef _WIN32\\nWSADATA wsaData;\\nint wsaDataInit = 0;\"\n", "},\n", "{\n", "\"file_name\": \"CWE789_Uncontrolled_Mem_Alloc__new_char_connect_socket_12.cpp\",\n", "\"CWE_ID\": \"CWE-789\",\n", "\"IF_VUL\": true,\n", "\"Description\": \"* CWE: 789 Uncontrolled Memory Allocation\",\n", "\"code\": \"void bad()\\n{\\nsize_t data;\\ndata = 0;\\nif(globalReturnsTrueOrFalse())\\n{\\n{\\n#ifdef _WIN32\\nWSADATA wsaData;\\nint wsaDataInit = 0;\"\n", "},\n", "{\n", "\"file_name\": \"CWE789_Uncontrolled_Mem_Alloc__new_char_connect_socket_12.cpp\",\n", "\"CWE_ID\": \"CWE-789\",\n", "\"IF_VUL\": false,\n", "\"Description\": \"* CWE: 789 Uncontrolled Memory Allocation\",\n", "\"code\": \"static void goodB2G()\\n{\\nsize_t data;\\ndata = 0;\\nif(globalReturnsTrueOrFalse())\\n{\\n{\\n#ifdef _WIN32\\nWSADATA wsaData;\\nint wsaDataInit = 0;\"\n", "},\n", "{\n", "\"file_name\": \"CWE789_Uncontrolled_Mem_Alloc__malloc_wchar_t_connect_socket_81_goodB2G.cpp\",\n", "\"CWE_ID\": \"CWE-789\",\n", "\"IF_VUL\": false,\n", "\"Description\": \"* CWE: 789 Uncontrolled Memory Allocation\",\n", "\"code\": \"#include \\\"std_testcase.h\\\"\\n#include \\\"func_1.h\\\"\\n#define HELLO_STRING L\\\"hello\\\"\\nnamespace func_1\\n{\\nvoid func_2::action(size_t data) const\\n{\\n{\\nwchar_t * myString;\\nif (data > wcslen(HELLO_STRING) && data < 100)\\n{\\nmyString = (wchar_t *)malloc(data*sizeof(wchar_t));\\nif (myString == NULL) {exit(-1);}\\nwcscpy(myString, HELLO_STRING);\\n\\nfree(myString);\\n}\\nelse\\n{\\n\\n}\\n}\\n}\\n}\"\n", "},\n", "{\n", "\"file_name\": \"CWE789_Uncontrolled_Mem_Alloc__malloc_char_fscanf_53d.c\",\n", "\"CWE_ID\": \"CWE-789\",\n", "\"IF_VUL\": true,\n", "\"Description\": \"* CWE: 789 Uncontrolled Memory Allocation\",\n", "\"code\": \"void func_1(size_t data)\\n{\\n{\\nchar * myString;\\nif (data > strlen(HELLO_STRING))\\n{\\nmyString = (char *)malloc(data*sizeof(char));\\nif (myString == NULL) {exit(-1);}\\nstrcpy(myString, HELLO_STRING);\\n\\nfree(myString);\\n}\\nelse\\n{\\n\\n}\\n}\\n}\"\n", "},\n", "{\n", "\"file_name\": \"CWE789_Uncontrolled_Mem_Alloc__malloc_char_fscanf_53d.c\",\n", "\"CWE_ID\": \"CWE-789\",\n", "\"IF_VUL\": false,\n", "\"Description\": \"* CWE: 789 Uncontrolled Memory Allocation\",\n", "\"code\": \"void func_1(size_t data)\\n{\\n{\\nchar * myString;\\nif (data > strlen(HELLO_STRING))\\n{\\nmyString = (char *)malloc(data*sizeof(char));\\nif (myString == NULL) {exit(-1);}\\nstrcpy(myString, HELLO_STRING);\\n\\nfree(myString);\\n}\\nelse\\n{\\n\\n}\\n}\\n}\\nvoid func_2(size_t data)\\n{\\n{\\nchar * myString;\\nif (data > strlen(HELLO_STRING) && data < 100)\\n{\\nmyString = (char *)malloc(data*sizeof(char));\\nif (myString == NULL) {exit(-1);}\\nstrcpy(myString, HELLO_STRING);\\n\\nfree(myString);\\n}\\nelse\\n{\\n\\n}\\n}\\n}\"\n", "},\n", "{\n", "\"file_name\": \"CWE789_Uncontrolled_Mem_Alloc__malloc_wchar_t_fgets_81_goodG2B.cpp\",\n", "\"CWE_ID\": \"CWE-789\",\n", "\"IF_VUL\": false,\n", "\"Description\": \"* CWE: 789 Uncontrolled Memory Allocation\",\n", "\"code\": \"#include \\\"std_testcase.h\\\"\\n#include \\\"func_1.h\\\"\\n#define HELLO_STRING L\\\"hello\\\"\\nnamespace func_1\\n{\\nvoid func_2::action(size_t data) const\\n{\\n{\\nwchar_t * myString;\\nif (data > wcslen(HELLO_STRING))\\n{\\nmyString = (wchar_t *)malloc(data*sizeof(wchar_t));\\nif (myString == NULL) {exit(-1);}\\nwcscpy(myString, HELLO_STRING);\\n\\nfree(myString);\\n}\\nelse\\n{\\n\\n}\\n}\\n}\\n}\"\n", "},\n", "{\n", "\"file_name\": \"CWE789_Uncontrolled_Mem_Alloc__malloc_wchar_t_rand_82a.cpp\",\n", "\"CWE_ID\": \"CWE-789\",\n", "\"IF_VUL\": true,\n", "\"Description\": \"* CWE: 789 Uncontrolled Memory Allocation\",\n", "\"code\": \"void bad()\\n{\\nsize_t data;\\ndata = 0;\\ndata = rand();\\nfunc_1* baseObject = new func_2;\\nbaseObject->action(data);\\ndelete baseObject;\\n}\"\n", "},\n", "{\n", "\"file_name\": \"CWE789_Uncontrolled_Mem_Alloc__malloc_wchar_t_rand_82a.cpp\",\n", "\"CWE_ID\": \"CWE-789\",\n", "\"IF_VUL\": false,\n", "\"Description\": \"* CWE: 789 Uncontrolled Memory Allocation\",\n", "\"code\": \"static void goodG2B()\\n{\\nsize_t data;\\ndata = 0;\\ndata = 20;\\nfunc_1* baseObject = new func_2;\\nbaseObject->action(data);\\ndelete baseObject;\\n}\\nstatic void goodB2G()\\n{\\nsize_t data;\\ndata = 0;\\ndata = rand();\\nfunc_1* baseObject = new func_3;\\nbaseObject->action(data);\\ndelete baseObject;\\n}\\nvoid good()\\n{\\ngoodG2B();\\ngoodB2G();\\n}\"\n", "},\n", "{\n"]}], "source": ["def read_json_first_100_lines(file_path):\n", "    \"\"\"\n", "    逐行读取 JSON 文件的前 100 行并打印\n", "    :param file_path: JSON 文件路径\n", "    \"\"\"\n", "    with open(file_path, 'r', encoding='utf-8') as file:\n", "        # 逐行遍历文件\n", "        for i, line in enumerate(file):\n", "            print(line.strip())  # 打印去掉两边多余空格的行\n", "            if i >= 99:  # 读取到 100 行后停止\n", "                break\n", "\n", "\n", "file_path = 'analyzed_files.json'\n", "read_json_first_100_lines(file_path)\n"]}, {"cell_type": "code", "execution_count": null, "id": "3930bac5-48fb-498e-813e-3123b876c896", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.8"}}, "nbformat": 4, "nbformat_minor": 5}