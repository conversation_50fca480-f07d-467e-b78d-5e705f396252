import Vue from 'vue'
import VueRouter from 'vue-router'
import Home from '../views/Home.vue'
import Repository from '../views/Repository.vue'
import Analysis from '../views/Analysis.vue'

Vue.use(VueRouter)

const routes = [
    {
        path: '/',
        name: 'home',
        component: Home
    },
    {
        path: '/repository',
        name: 'repository',
        component: Repository
    },
    {
        path: '/analysis',
        name: 'analysis',
        component: Analysis
    },
    {
    path: '/dashboard',
    name: 'VulnerabilityDashboard',
    component: () => import('@/views/VulnerabilityDashboard.vue'),
    meta: {
        title: '漏洞分析仪表板',
        icon: 'el-icon-data-analysis'
    }
}
]

const router = new VueRouter({
    mode: 'history',
    base: process.env.BASE_URL,
    routes
})

export default router
