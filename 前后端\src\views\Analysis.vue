<template>
    <div class="analysis-page">
        <!-- 添加面包屑导航 -->
        <el-breadcrumb separator-class="el-icon-arrow-right" style="margin-bottom: 20px">
            <el-breadcrumb-item :to="{ path: '/repository' }">代码仓库</el-breadcrumb-item>
            <el-breadcrumb-item v-if="currentRepository">{{ currentRepository.name }}</el-breadcrumb-item>
            <el-breadcrumb-item v-if="selectedFile">{{ selectedFile.name }}</el-breadcrumb-item>
        </el-breadcrumb>

        <el-row :gutter="20">
            <!-- 代码查看区域 -->
            <el-col :span="12">
                <el-card class="code-section">
                    <div slot="header" class="code-header">
                        <span v-if="selectedFile">
                            <i class="el-icon-document"></i>
                            {{ selectedFile.name }}
                        </span>
                        <span v-else>代码查看</span>
                        
                        <div class="header-actions">
                            <el-button
                                v-if="selectedFile"
                                size="mini"
                                type="text"
                                icon="el-icon-refresh"
                                @click="reloadFile"
                            >
                                刷新
                            </el-button>
                            <el-button
                                v-if="selectedFile"
                                size="mini"
                                type="text"
                                icon="el-icon-folder-opened"
                                @click="selectAnotherFile"
                            >
                                选择其他文件
                            </el-button>
                        </div>
                    </div>

                    <code-viewer
                        v-if="selectedFile && fileContent"
                        ref="codeViewer"
                        :file="selectedFile"
                        :content="fileContent"
                        :highlights="analysisHighlights"
                        @code-selected="handleCodeSelection"
                        @vulnerability-clicked="handleViewVulnerability"
                    />

                    <div v-else-if="loading" class="loading-message">
                        <i class="el-icon-loading"></i>
                        <p>正在加载文件内容...</p>
                    </div>

                    <div v-else-if="loadError" class="error-message">
                        <i class="el-icon-warning"></i>
                        <p>{{ loadError }}</p>
                        <el-button type="primary" size="small" @click="reloadFile">
                            重试
                        </el-button>
                    </div>

                    <div v-else class="no-file-message">
                        <i class="el-icon-document"></i>
                        <p>请先选择要分析的文件</p>
                        <el-button type="primary" @click="goToRepository">
                            前往仓库选择文件
                        </el-button>
                    </div>
                </el-card>
            </el-col>

            <!-- 分析面板 -->
            <el-col :span="12">
                <analysis-panel
                    v-if="selectedFile && fileContent"
                    :file="selectedFile"
                    :file-content="fileContent"
                    :selected-code="selectedCode"
                    :repository="currentRepository"
                    @analysis-complete="handleAnalysisComplete"
                />
                
                <!-- 快速操作面板 -->
                <el-card v-else-if="!selectedFile" class="quick-actions">
                    <div slot="header">
                        <span>快速操作</span>
                    </div>
                    <div class="action-buttons">
                        <el-button 
                            type="primary" 
                            icon="el-icon-folder-opened"
                            @click="goToRepository"
                            size="medium"
                        >
                            选择代码文件
                        </el-button>
                        <el-button 
                            type="success" 
                            icon="el-icon-time"
                            @click="showHistory = true"
                            size="medium"
                            :disabled="savedResults.length === 0"
                        >
                            查看历史记录 ({{ savedResults.length }})
                        </el-button>
                    </div>
                </el-card>
            </el-col>
        </el-row>

        <!-- 分析结果展示 -->
        <el-row :gutter="20" v-if="analysisResults.length > 0" style="margin-top: 20px">
            <el-col :span="24">
                <result-display
                    :results="analysisResults"
                    :file="selectedFile"
                    :repository="currentRepository"
                    @save-result="saveResult"
                    @export-result="exportResult"
                    @go-to-code="handleGoToCode"
                    @vulnerability-fixed="handleVulnerabilityFixed"
                    @clear-results="clearResults"
                />
            </el-col>
        </el-row>

        <!-- 历史分析记录对话框 -->
        <el-dialog
            title="历史分析记录"
            :visible.sync="showHistory"
            width="80%"
            top="5vh"
        >
            <el-table 
                :data="savedResults" 
                style="width: 100%"
                max-height="500"
            >
                <el-table-column 
                    prop="repository" 
                    label="仓库" 
                    width="150"
                />
                <el-table-column 
                    prop="filePath" 
                    label="文件路径" 
                    min-width="200"
                    show-overflow-tooltip
                />
                <el-table-column label="漏洞统计" width="150">
                    <template slot-scope="scope">
                        <el-tag 
                            :type="scope.row.summary.high > 0 ? 'danger' : 'success'"
                            size="small"
                        >
                            总计: {{ scope.row.summary.total }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column 
                    prop="savedAt" 
                    label="分析时间" 
                    width="180"
                >
                    <template slot-scope="scope">
                        {{ formatTime(scope.row.savedAt) }}
                    </template>
                </el-table-column>
                <el-table-column label="操作" width="150" fixed="right">
                    <template slot-scope="scope">
                        <el-button 
                            size="mini" 
                            type="primary"
                            @click="viewHistoryRecord(scope.row)"
                        >
                            查看
                        </el-button>
                        <el-button 
                            size="mini" 
                            type="danger"
                            @click="deleteHistoryRecord(scope.row, scope.$index)"
                        >
                            删除
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
        </el-dialog>
    </div>
</template>

<script>
import CodeViewer from '@/components/CodeViewer.vue'
import AnalysisPanel from '@/components/AnalysisPanel.vue'
import ResultDisplay from '@/components/ResultDisplay.vue'
import { mapState, mapActions } from 'vuex'
import api from "@/api"

export default {
    name: 'Analysis',
    components: {
        CodeViewer,
        AnalysisPanel,
        ResultDisplay
    },
    data() {
        return {
            selectedCode: null,
            analysisHighlights: [],
            fileContent: null,
            analysisResults: [],
            savedResults: [],
            loading: false,
            loadError: null,
            showHistory: false
        }
    },
    computed: {
        ...mapState(['selectedFile', 'currentRepository'])
    },
    watch: {
        selectedFile: {
            immediate: true,
            async handler(newFile) {
                if (newFile) {
                    await this.loadFileContent()
                    // 清空之前的分析结果
                    this.analysisResults = []
                    this.analysisHighlights = []
                }
            }
        }
    },
    created() {
        // 尝试从路由参数恢复状态
        this.restoreFromRoute()
    },
    mounted() {
        // 加载保存的分析结果
        this.loadSavedResults()
        
        // 如果没有选中的仓库，尝试从 localStorage 恢复
        if (!this.currentRepository) {
            this.restoreRepository()
        }
    },
    methods: {
        ...mapActions(['saveAnalysisResult']),

        // 从路由参数恢复状态
        async restoreFromRoute() {
            const { repository, filePath } = this.$route.params
            
            if (repository && filePath && !this.selectedFile) {
                // 尝试恢复仓库和文件
                try {
                    const repoResponse = await api.getRepository(repository)
                    if (repoResponse.success) {
                        this.$store.commit('SET_CURRENT_REPOSITORY', repoResponse.data)
                        
                        // 查找并设置文件
                        const file = this.findFileInTree(repoResponse.data.files, filePath)
                        if (file) {
                            this.$store.commit('SET_SELECTED_FILE', file)
                        }
                    }
                } catch (error) {
                    console.error('Failed to restore from route:', error)
                }
            }
        },

        // 从 localStorage 恢复仓库
        async restoreRepository() {
            const lastRepository = localStorage.getItem('lastSelectedRepository')
            if (lastRepository) {
                try {
                    const response = await api.getRepository(lastRepository)
                    if (response.success) {
                        this.$store.commit('SET_CURRENT_REPOSITORY', response.data)
                    }
                } catch (error) {
                    console.error('Failed to restore repository:', error)
                }
            }
        },

        // 在文件树中查找文件
        findFileInTree(tree, path) {
            for (const item of tree) {
                if (item.path === path && item.type === 'file') {
                    return item
                }
                if (item.children) {
                    const found = this.findFileInTree(item.children, path)
                    if (found) return found
                }
            }
            return null
        },

        async loadFileContent() {
            if (!this.selectedFile) return
            
            this.loading = true
            this.loadError = null
            this.fileContent = null
            
            try {
                // 检查文件是否存在完整路径
                if (!this.selectedFile.fullPath && this.currentRepository) {
                    // 重建完整路径
                    this.selectedFile.fullPath = path.join(
                        this.currentRepository.extractPath, 
                        this.selectedFile.path
                    )
                }

                if (this.selectedFile.zipEntry) {
                    this.fileContent = await this.selectedFile.zipEntry.async('string')
                } else if (this.selectedFile.tarEntry) {
                    const decoder = new TextDecoder('utf-8')
                    this.fileContent = decoder.decode(this.selectedFile.tarEntry.data)
                } else {
                    const response = await api.getFileContent(this.selectedFile.fullPath)
                    if (response.success) {
                        this.fileContent = response.content
                        if (!this.fileContent) {
                            throw new Error('文件内容为空')
                        }
                    } else {
                        throw new Error(response.error || '加载失败')
                    }
                }
            } catch (error) {
                this.loadError = '加载文件内容失败：' + error.message
                this.$message.error(this.loadError)
                console.error('Load file error:', error)
            } finally {
                this.loading = false
            }
        },

        reloadFile() {
            this.loadFileContent()
        },

        goToRepository() {
            this.$router.push({ name: 'repository' })
        },

        selectAnotherFile() {
            this.$router.push({ name: 'repository' })
        },

        clearResults() {
            this.analysisResults = []
            this.analysisHighlights = []
        },

        handleCodeSelection(codeInfo) {
            this.selectedCode = codeInfo
        },

        handleAnalysisComplete(result) {
            // 将新的分析结果添加到数组中
            this.analysisResults = [result]

            // 处理所有漏洞，生成高亮显示
            const allHighlights = []
            
            if (result.vulnerabilities && result.vulnerabilities.length > 0) {
                result.vulnerabilities.forEach(vuln => {
                    // 解析行号信息
                    let startLine = vuln.startLine || 0
                    let endLine = vuln.endLine || 0
                    
                    // 如果是 LLM 分析结果，从 "在文件中的行数" 字段提取行号
                    if (vuln.isLLMConfirmed && vuln.reasoning) {
                        const lineMatch = vuln.reasoning.match(/(\d+)行/)
                        if (lineMatch) {
                            const lineNum = parseInt(lineMatch[1])
                            startLine = lineNum
                            endLine = lineNum
                        }
                    }
                    
                    allHighlights.push({
                        id: vuln.id,
                        startLine: startLine,
                        endLine: endLine,
                        severity: vuln.severity,
                        message: vuln.message || vuln.reasoning,
                        type: vuln.type,
                        cwe: vuln.cwe,
                        tool: vuln.tool,
                        reasoning: vuln.reasoning,
                        lineInfo: vuln.lineInfo
                    })
                })
            }
            
            this.analysisHighlights = allHighlights

            // 显示分析完成的统计信息
            this.showAnalysisNotification(result)
        },

        showAnalysisNotification(result) {
            const llmCount = result.vulnerabilities.filter(v => v.isLLMConfirmed).length
            const totalCount = result.vulnerabilities.length
            
            if (llmCount > 0) {
                this.$notify({
                    title: '分析完成',
                    message: `发现 ${totalCount} 个潜在问题，其中 ${llmCount} 个被 AI 确认为漏洞`,
                    type: 'warning',
                    duration: 5000
                })
            } else if (totalCount > 0) {
                this.$notify({
                    title: '分析完成',
                    message: `发现 ${totalCount} 个潜在问题`,
                    type: 'info',
                    duration: 5000
                })
            } else {
                this.$notify({
                    title: '分析完成',
                    message: '未发现安全问题',
                    type: 'success',
                    duration: 3000
                })
            }
        },

        handleViewVulnerability(vulnerability) {
            // 解析行号
            let lineToScroll = vulnerability.startLine
            
            if (!lineToScroll && vulnerability.lineInfo) {
                const lineMatch = vulnerability.lineInfo.match(/(\d+)/)
                if (lineMatch) {
                    lineToScroll = parseInt(lineMatch[1])
                }
            }
            
            if (lineToScroll && this.$refs.codeViewer) {
                this.$refs.codeViewer.scrollToLine(lineToScroll)
            }

            // 高亮显示漏洞行
            const highlightIndex = this.analysisHighlights.findIndex(h => h.id === vulnerability.id)
            if (highlightIndex !== -1) {
                // 先将所有高亮的 active 设为 false
                this.analysisHighlights.forEach(h => h.active = false)
                // 再将当前漏洞的 active 设为 true
                this.analysisHighlights[highlightIndex].active = true
                
                // 触发重新渲染
                this.analysisHighlights = [...this.analysisHighlights]
            }
        },

        handleGoToCode(vulnerability) {
            this.handleViewVulnerability(vulnerability)
        },

        handleVulnerabilityFixed(vulnerability) {
            // 更新高亮显示
            const highlight = this.analysisHighlights.find(h => h.id === vulnerability.id)
            if (highlight) {
                highlight.severity = 'info'
                highlight.message = `[已修复] ${highlight.message}`
            }
        },

        saveResult(result) {
            const enrichedResult = {
                ...result,
                savedAt: new Date(),
                repository: this.currentRepository?.name,
                filePath: this.selectedFile?.path
            }

            this.saveAnalysisResult(enrichedResult)
            this.savedResults.unshift(enrichedResult)
            
            // 保存到本地存储
            this.saveToLocalStorage()
            
            this.$message.success('分析结果已保存')
        },

        exportResult(report) {
            // 生成更详细的导出报告
            const enrichedReport = {
                ...report,
                metadata: {
                    ...report.metadata,
                    repository: this.currentRepository?.name,
                    analyzedFile: this.selectedFile?.path,
                    fileContent: this.fileContent
                }
            }

            const dataStr = JSON.stringify(enrichedReport, null, 2)
            const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr)

            const timestamp = new Date().toISOString().replace(/[:.-]/g, '')
            const exportFileName = `vulnerability_report_${this.selectedFile.name}_${timestamp}.json`

            const linkElement = document.createElement('a')
            linkElement.setAttribute('href', dataUri)
            linkElement.setAttribute('download', exportFileName)
            linkElement.click()

            this.$message.success('分析报告已导出')
        },

        loadSavedResults() {
            const saved = localStorage.getItem('analysisResults')
            if (saved) {
                try {
                    this.savedResults = JSON.parse(saved)
                } catch (e) {
                    console.error('Failed to load saved results:', e)
                    this.savedResults = []
                }
            }
        },

        saveToLocalStorage() {
            localStorage.setItem('analysisResults', JSON.stringify(this.savedResults))
        },

        viewHistoryRecord(record) {
            this.showHistory = false
            this.analysisResults = [record]
            this.analysisHighlights = []
            
            // 重新生成高亮
            if (record.vulnerabilities) {
                this.analysisHighlights = record.vulnerabilities.map(vuln => ({
                    id: vuln.id,
                    startLine: vuln.startLine,
                    endLine: vuln.endLine,
                    severity: vuln.severity,
                    message: vuln.message || vuln.reasoning,
                    type: vuln.type,
                    cwe: vuln.cwe,
                    reasoning: vuln.reasoning
                }))
            }
            
            this.$message.info('已加载历史分析记录')
        },

        deleteHistoryRecord(record, index) {
            this.$confirm('确定要删除这条历史记录吗？', '删除确认', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.savedResults.splice(index, 1)
                this.saveToLocalStorage()
                this.$message.success('删除成功')
            }).catch(() => {})
        },

        formatTime(date) {
            return new Date(date).toLocaleString('zh-CN')
        }
    }
}
</script>

<style scoped>
.analysis-page {
    padding: 20px;
    background-color: #f5f7fa;
    min-height: calc(100vh - 60px);
}

.code-section {
    height: 700px;
    display: flex;
    flex-direction: column;
}

.code-section >>> .el-card__body {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.code-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header-actions {
    display: flex;
    gap: 10px;
}

.no-file-message,
.loading-message,
.error-message {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    color: #909399;
}

.error-message {
    color: #F56C6C;
}

.no-file-message i,
.loading-message i,
.error-message i {
    font-size: 64px;
    margin-bottom: 20px;
}

.loading-message i {
    animation: rotate 1s linear infinite;
}

@keyframes rotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

.no-file-message p,
.loading-message p,
.error-message p {
    margin-bottom: 20px;
    font-size: 16px;
}

.quick-actions {
    height: 200px;
}

.action-buttons {
    display: flex;
    flex-direction: column;
    gap: 20px;
    align-items: center;
    padding: 20px;
}

.el-row {
    margin-bottom: 20px;
}

.el-col {
    min-height: 600px;
}

/* 面包屑样式 */
.el-breadcrumb {
    background-color: white;
    padding: 16px;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
}

/* 对话框表格样式 */
::v-deep .el-dialog__body {
    padding: 10px 20px 20px;
}

::v-deep .el-table {
    font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .el-col {
        min-height: auto;
        margin-bottom: 20px;
    }
    
    .code-section {
        height: 500px;
    }
}
</style>
