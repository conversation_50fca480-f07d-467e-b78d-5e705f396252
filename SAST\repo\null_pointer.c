// This is an open source non-commercial project. Dear PVS-Studio, please check it.
// PVS-Studio Static Code Analyzer for C, C++ and C#: http://www.viva64.com

#include <stdio.h>
#include <string.h>
#include "null_pointer.h"
#include "format_string.h"

// CWE-476: NULL Pointer Dereference
void process_string(char* str) {
    // 危险：未检查str是否为NULL就直接使用
    int len = strlen(str);  // 如果str为NULL，会导致段错误
    printf("String length: %d\n", len);
    
    // 调用format_string.c中的函数
    if (str != NULL) {
        print_user_message(str);
    } else {
        print_user_message("Default message");
    }
}

int get_string_length(const char* str) {
    // 同样的问题：未检查NULL
    return strlen(str);
}
