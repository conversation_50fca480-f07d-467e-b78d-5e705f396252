import os
import json
import re

def remove_main_blocks(content):
    """
    Remove blocks enclosed by #ifdef INCLUDEMAIN and #endif (including these lines themselves).
    
    Args:
        content (str): The content of the file as a string.
    
    Returns:
        str: The content after removing any #ifdef INCLUDEMAIN blocks.
    """
    lines = content.splitlines()  # Split content into lines
    cleaned_lines = []
    inside_includemain = False
    nesting_level = 0  # To keep track of nested #ifdef INCLUDEMAIN

    for line in lines:
        # Check for #ifdef INCLUDEMAIN
        if "#ifdef INCLUDEMAIN" in line:
            inside_includemain = True
            nesting_level += 1
            continue  # Skip this line

        # Check for nested preprocessor directives
        if inside_includemain and "#ifndef" in line:
            nesting_level += 1
        if inside_includemain and "#endif" in line:
            nesting_level -= 1
            if nesting_level == 0:
                inside_includemain = False
            continue  # Skip this line

        # Append lines outside of INCLUDEMAIN block
        if not inside_includemain:
            cleaned_lines.append(line)
    print(cleaned_lines)
    return "\n".join(cleaned_lines)


def extract_code(content, start_tag, end_tag):
    """
    Extract code blocks between start_tag and the matching end_tag,
    accounting for nested #ifdef/#ifndef sections.
    """
    code_blocks = []
    lines = content.splitlines()  # Split content into lines
    stack = []  # Stack to handle nesting
    current_block = []  # To store the lines of the current block

    for line in lines:
        # Check for the start tag
        if start_tag in line:
            if len(stack) == 0:  # Top-level start tag found
                current_block = []  # Start a new block
            stack.append(start_tag)  # Push onto the stack

        # Always append lines when inside a block
        if len(stack) > 0:
            current_block.append(line)

        # Check for the end tag
        if end_tag in line and len(stack) > 0:
            stack.pop()  # Pop the stack
            if len(stack) == 0:  # Top-level block finished
                
                code_blocks.append("\n".join(current_block))  # Store the block
                current_block = []  # Reset for next block
    

    return code_blocks

def clean_code_block(code_block):
    """
    Clean the code block by removing comments and extra lines.
    """
    # Remove single-line comments (// ...)
    code_block = re.sub(r"//.*", "", code_block)
    # Remove multi-line comments (/* ... */)
    code_block = re.sub(r"/\*[\s\S]*?\*/", "", code_block)
    # Strip extra whitespace lines
    code_block = "\n".join([line.strip() for line in code_block.splitlines() if line.strip()])
    return code_block

def remove_print_statements(code_block):
    """
    Remove all print-related function calls from the code block.
    Examples:
        print("Hello, World");
        printLine("This is a test");
    """
    # Regex to match print-like function calls
    print_patterns = [
        r"\bprint\w*\s*\([^;]*\);",  # Matches print or printLine calls like "print(...);" or "printLine(...);"
    ]

    for pattern in print_patterns:
        code_block = re.sub(pattern, "", code_block)

    return code_block

def is_include_main_block(code_block):
    """
    Check if the given code block contains "#ifdef INCLUDEMAIN" or similar main program logic.
    """
    main_function_patterns = [
        r"#ifdef\s+INCLUDEMAIN",       # Check for #ifdef INCLUDEMAIN
        r"int\s+main\s*\(",           # Check for main function declaration
        r"seed randomness"            # Check for seed randomness comments
    ]
    for pattern in main_function_patterns:
        if re.search(pattern, code_block, re.IGNORECASE):
            return True
    return False

def remove_preprocessor_directives(code_block, start_macro):
    """
    Remove the #ifndef <start_macro> if it's the first line 
    and the corresponding #endif if it's the last line.
    
    Args:
        code_block (str): The input code block as a string.
        start_macro (str): The macro to check (e.g., "OMITBAD" or "OMITGOOD").
    
    Returns:
        str: The modified code block with directives removed (if present).
    """
    lines = code_block.splitlines()
    
    # Check the first line
    if lines and lines[0].strip() == f"#ifndef {start_macro}":
        lines.pop(0)  # Remove the first line

    # Check the last line
    if lines and lines[-1].strip() == "#endif":
        lines.pop(-1)  # Remove the last line

    # Rejoin the remaining lines into a single string
    return "\n".join(lines)


def anonymize_cwe_functions(code_block):
    """
    Anonymize all functions with 'CWE' in their name.
    """
    placeholder_mapping = {}  # Map to store function name and its placeholder
    function_pattern = r"\b(CWE\w+)\b"  # Match functions or identifiers starting with CWE

    def replace_function_name(match):
        """
        Replace matched CWE function with a consistent placeholder.
        """
        function_name = match.group(1)
        if function_name not in placeholder_mapping:
            placeholder_mapping[function_name] = f"func_{len(placeholder_mapping) + 1}"
        return placeholder_mapping[function_name]

    # Replace all CWE-prefixed functions with placeholders
    anonymized_code = re.sub(function_pattern, replace_function_name, code_block)
    return anonymized_code

def extract_cwe_id_from_filename(file_name):
    """
    Extract CWE ID from the filename (e.g., CWE176_XXXX.cpp -> CWE-176).
    """
    match = re.search(r"CWE(\d+)", file_name)
    if match:
        return f"CWE-{match.group(1)}"
    return "Unknown"

def analyze_file(file_path):
    """
    Analyze a given file for CWE ID, IF_VUL, and code blocks.
    """
    results = []
    file_name = os.path.basename(file_path)
    cwe_id = extract_cwe_id_from_filename(file_name)  # Extract CWE ID from the file name

    with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
        content = f.read()

        # Extract Description from comments
        description_match = re.search(r"@description\s*([\s\S]*?)\n", content)
        description = description_match.group(1).strip() if description_match else "No description available."
        
        cleaned_content = remove_main_blocks(content)

        # Extract "BAD" and "GOOD" code blocks
        bad_code_blocks = extract_code(cleaned_content, r"#ifndef OMITBAD", r"#endif")
        good_code_blocks = extract_code(cleaned_content, r"#ifndef OMITGOOD", r"#endif")

        # Populate BAD entries
        for bad_code in bad_code_blocks:
            bad_code = clean_code_block(bad_code)
            bad_code = anonymize_cwe_functions(bad_code)
            bad_code = remove_print_statements(bad_code)
            bad_code = remove_preprocessor_directives(bad_code,"OMITBAD")
            results.append({
                "file_name": file_name,
                "CWE_ID": cwe_id,
                "IF_VUL": True,
                "Description": description,
                "code": bad_code
            })

        # Populate GOOD entries
        for good_code in good_code_blocks:
            good_code = clean_code_block(good_code)
            good_code = anonymize_cwe_functions(good_code)
            good_code = remove_print_statements(good_code)
            good_code = remove_preprocessor_directives(good_code,"OMITGOOD")
            results.append({
                "file_name": file_name,
                "CWE_ID": cwe_id,
                "IF_VUL": False,
                "Description": description,
                "code": good_code
            })

    return results

def process_directory(directory):
    """
    Traverse the directory to find .c and .cpp files and analyze them.
    """
    files_data = []
    for root, _, files in os.walk(directory):
        for file in files:
            if file.endswith(".c") or file.endswith(".cpp"):
                file_path = os.path.join(root, file)
                file_data = analyze_file(file_path)
                files_data.extend(file_data)  # Add `BAD` and `GOOD` entries separately
    return files_data

def main(input_directory, output_json):
    """
    Main script execution.
    """
    all_files_data = process_directory(input_directory)
    with open(output_json, 'w', encoding='utf-8') as f:
        json.dump(all_files_data, f, indent=4, ensure_ascii=False)
    print(f"JSON output successfully written to {output_json}")

if __name__ == "__main__":
    # Define your input directory and output JSON file
    input_directory = "./output/C/testcases"  # Replace with your directory containing .c/.cpp files
    output_json = "analyzed_files.json"
    
    main(input_directory, output_json)
