#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include "memory_leak.h"
#include "integer_overflow.h"

// CWE-401: Missing Release of Memory after Effective Lifetime
void store_data(const char* data) {
    char* buffer = (char*)malloc(256);  // 分配内存
    if (buffer != NULL) {
        strcpy(buffer, data);
        printf("Data stored: %s\n", buffer);
        // 错误：未释放分配的内存，导致内存泄漏
        // 应该调用 free(buffer);
    }
    
    // 调用integer_overflow.c中的函数
    int size = calculate_buffer_size(strlen(data));
    printf("Calculated size: %d\n", size);
}

char* allocate_buffer(int size) {
    return (char*)malloc(size);
}
