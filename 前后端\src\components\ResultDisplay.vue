<template>
    <div class="result-display">
        <el-card>
            <div slot="header" class="result-header">
                <span>分析结果</span>
                <div class="result-actions">
                    <el-button size="small" type="primary" @click="saveResults">
                        保存结果
                    </el-button>
                    <el-button size="small" @click="exportResults">
                        导出报告
                    </el-button>
                </div>
            </div>

            <!-- 结果概览 -->
            <div class="result-summary">
                <el-row :gutter="20">
                    <el-col :span="6">
                        <el-statistic title="总漏洞数" :value="totalVulnerabilities" />
                    </el-col>
                    <el-col :span="6">
                        <el-statistic
                                title="高危漏洞"
                                :value="highSeverityCount"
                                value-style="color: #f56c6c"
                        />
                    </el-col>
                    <el-col :span="6">
                        <el-statistic
                                title="中危漏洞"
                                :value="mediumSeverityCount"
                                value-style="color: #e6a23c"
                        />
                    </el-col>
                    <el-col :span="6">
                        <el-statistic
                                title="低危漏洞"
                                :value="lowSeverityCount"
                                value-style="color:#409eff"
                        />
                    </el-col>
                </el-row>
            </div>

            <!-- LLM 分析总结 -->
            <div v-if="hasLLMAnalysis" class="llm-summary">
                <h3>
                    <i class="el-icon-s-opportunity"></i>
                    LLM 智能分析
                </h3>
                <el-alert
                    :title="`检测到 ${llmDetectedCount} 个确认漏洞`"
                    :type="llmDetectedCount > 0 ? 'error' : 'success'"
                    :description="llmSummary"
                    show-icon
                    :closable="false"
                />
            </div>

            <!-- 漏洞列表 -->
            <div class="vulnerability-list">
                <h3>漏洞详情</h3>
                
                <!-- 过滤器 -->
                <div class="filter-bar">
                    <el-radio-group v-model="filterType" size="small">
                        <el-radio-button label="all">全部</el-radio-button>
                        <el-radio-button label="llm">LLM确认</el-radio-button>
                        <!-- <el-radio-button label="sast">静态分析</el-radio-button>
                        <el-radio-button label="similarity">相似度匹配</el-radio-button> -->
                    </el-radio-group>
                </div>

                <el-table :data="filteredVulnerabilities" style="width: 100%">
                    <el-table-column type="index" width="50" />
                    
                    <el-table-column prop="tool" label="来源" width="120">
                        <template slot-scope="scope">
                            <el-tag size="small" :type="getToolType(scope.row.tool)">
                                {{ scope.row.tool }}
                            </el-tag>
                        </template>
                    </el-table-column>
                    
                    <el-table-column prop="cwe" label="CWE" width="100">
                        <template slot-scope="scope">
                            <span v-if="scope.row.cwe && scope.row.cwe !== 'N/A'">
                                CWE-{{ scope.row.cwe }}
                            </span>
                            <span v-else>-</span>
                        </template>
                    </el-table-column>
                    
                    <el-table-column prop="severity" label="严重程度" width="100">
                        <template slot-scope="scope">
                            <el-tag
                                    :type="getSeverityType(scope.row.severity)"
                                    size="small"
                            >
                                {{ getSeverityText(scope.row.severity) }}
                            </el-tag>
                        </template>
                    </el-table-column>
                    
                    <el-table-column label="位置" width="120">
                        <template slot-scope="scope">
                            <span v-if="scope.row.lineInfo">
                                {{ scope.row.lineInfo }}
                            </span>
                            <span v-else-if="scope.row.startLine">
                                行 {{ scope.row.startLine }}-{{ scope.row.endLine }}
                            </span>
                            <span v-else>-</span>
                        </template>
                    </el-table-column>
                    
                    <el-table-column prop="message" label="描述" show-overflow-tooltip>
                        <template slot-scope="scope">
                            <span v-if="scope.row.isLLMConfirmed" style="color: #f56c6c;">
                                <i class="el-icon-warning"></i>
                            </span>
                            {{ scope.row.message || scope.row.reasoning }}
                        </template>
                    </el-table-column>
                    
                    <el-table-column label="操作" width="150" fixed="right">
                        <template slot-scope="scope">
                            <el-button
                                    size="mini"
                                    @click="viewDetail(scope.row)"
                            >
                                查看详情
                            </el-button>
                            <!-- <el-button
                                    size="mini"
                                    type="success"
                                    @click="markAsFixed(scope.row)"
                                    v-if="!scope.row.fixed"
                            >
                                标记修复
                            </el-button> -->
                        </template>
                    </el-table-column>
                </el-table>
            </div>

            <!-- 详情对话框 -->
            <el-dialog
                    :title="currentVulnerability ? getVulnerabilityTitle(currentVulnerability) : ''"
                    :visible.sync="detailDialogVisible"
                    width="70%"
            >
                <div v-if="currentVulnerability" class="vulnerability-detail">
                    <el-descriptions :column="2" border>
                        <el-descriptions-item label="检测工具">
                            <el-tag :type="getToolType(currentVulnerability.tool)">
                                {{ currentVulnerability.tool }}
                            </el-tag>
                        </el-descriptions-item>
                        
                        <el-descriptions-item label="CWE编号">
                            <span v-if="currentVulnerability.cwe && currentVulnerability.cwe !== 'N/A'">
                                CWE-{{ currentVulnerability.cwe }}
                                 </span>
                            <span v-else>未指定</span>
                            
                        </el-descriptions-item>
                        
                        <el-descriptions-item label="严重程度">
                            <el-tag :type="getSeverityType(currentVulnerability.severity)">
                                {{ getSeverityText(currentVulnerability.severity) }}
                            </el-tag>
                        </el-descriptions-item>
                        
                        <el-descriptions-item label="代码位置">
                            <span v-if="currentVulnerability.lineInfo">
                                {{ currentVulnerability.lineInfo }}
                            </span>
                            <span v-else-if="currentVulnerability.startLine">
                                行 {{ currentVulnerability.startLine }}-{{ currentVulnerability.endLine }}
                            </span>
                            <span v-else>未指定</span>
                        </el-descriptions-item>
                        
                        <el-descriptions-item label="文件路径" :span="2">
                            {{ currentVulnerability.file }}
                        </el-descriptions-item>
                        
                        <el-descriptions-item label="问题描述" :span="2">
                            {{ currentVulnerability.message }}
                        </el-descriptions-item>
                        
                        <el-descriptions-item label="详细分析" :span="2" v-if="currentVulnerability.reasoning">
                            <div class="reasoning-content">
                                {{ currentVulnerability.reasoning }}
                            </div>
                        </el-descriptions-item>
                        
                        <el-descriptions-item label="相似度信息" :span="2" v-if="currentVulnerability.isSimilarityAnalysis">
                            <el-tag type="info">
                                相似度: {{ (1 - currentVulnerability.distance).toFixed(3) }}
                            </el-tag>
                            <span style="margin-left: 10px;">
                                参考文件: {{ currentVulnerability.code }}
                            </span>
                        </el-descriptions-item>
                        
                        <el-descriptions-item label="修复建议" :span="2">
                            <div v-if="currentVulnerability.suggestion">
                                {{ currentVulnerability.suggestion }}
                            </div>
                            <div v-else>
                                {{ getDefaultSuggestion(currentVulnerability) }}
                            </div>
                        </el-descriptions-item>
                    </el-descriptions>

                    <div class="code-preview" v-if="currentVulnerability.codeSnippet || currentVulnerability.similarCode">
                        <h4>相关代码</h4>
                        <pre><code>{{ currentVulnerability.codeSnippet || currentVulnerability.similarCode }}</code></pre>
                    </div>

                    <!-- LLM 分析详情 -->
                    <div v-if="currentVulnerability.isLLMConfirmed" class="llm-detail">
                        <el-divider>LLM 分析详情</el-divider>
                        <el-alert
                            title="此漏洞已被 AI 模型确认"
                            type="error"
                            :closable="false"
                            show-icon
                        >
                            <div slot="description">
                                <p><strong>模型:</strong> {{ currentVulnerability.tool }}</p>
                                <p><strong>推理过程:</strong> {{ currentVulnerability.reasoning }}</p>
                            </div>
                        </el-alert>
                    </div>
                </div>

                <span slot="footer" class="dialog-footer">
                    <el-button @click="detailDialogVisible = false">关闭</el-button>
                    <el-button type="primary" @click="goToCode(currentVulnerability)">
                        查看源码
                    </el-button>
                </span>
            </el-dialog>
        </el-card>
    </div>
</template>

<script>
export default {
    name: 'ResultDisplay',
    props: {
        results: {
            type: Array,
            required: true
        }
    },
    data() {
        return {
            detailDialogVisible: false,
            currentVulnerability: null,
            filterType: 'all'
        }
    },
    computed: {
        vulnerabilities() {
            const allVulns = []
            
            this.results.forEach(result => {
                if (result.vulnerabilities && Array.isArray(result.vulnerabilities)) {
                    // 处理每个漏洞，添加行号信息
                    const processedVulns = result.vulnerabilities.map(vuln => {
                        // 如果是 LLM 确认的漏洞，提取行号信息
                        let lineInfo = ''
                        if (vuln.isLLMConfirmed && vuln.reasoning) {
                            // 从推理过程中提取行号
                            const lineMatch = vuln.reasoning.match(/(\d+)行/)
                            if (lineMatch) {
                                lineInfo = lineMatch[0]
                            }
                        }
                        
                        return {
                            ...vuln,
                            fileName: result.fileName,
                            lineInfo: lineInfo || vuln.lineInfo
                        }
                    })
                    
                    allVulns.push(...processedVulns)
                }
            })
            
            return allVulns
        },

        filteredVulnerabilities() {
            if (this.filterType === 'all') {
                return this.vulnerabilities
            }
            
            switch (this.filterType) {
                case 'llm':
                    return this.vulnerabilities.filter(v => v.isLLMConfirmed)
                case 'sast':
                    return this.vulnerabilities.filter(v => v.tool === 'PVS-Studio')
                case 'similarity':
                    return this.vulnerabilities.filter(v => v.isSimilarityAnalysis)
                default:
                    return this.vulnerabilities
            }
        },

        totalVulnerabilities() {
            return this.vulnerabilities.length
        },

        highSeverityCount() {
            return this.vulnerabilities.filter(v => v.severity === 'high').length
        },

        mediumSeverityCount() {
            return this.vulnerabilities.filter(v => v.severity === 'medium').length
        },

        lowSeverityCount() {
            return this.vulnerabilities.filter(v => v.severity === 'low' || v.severity === 'info').length
        },

        hasLLMAnalysis() {
            return this.results.some(r => r.llmInsights)
        },

        llmDetectedCount() {
            return this.vulnerabilities.filter(v => v.isLLMConfirmed).length
        },

        llmSummary() {
            const llmResult = this.results.find(r => r.llmInsights)
            if (llmResult && llmResult.llmInsights) {
                return llmResult.llmInsights.summary
            }
            return ''
        }
    },
    methods: {
        getSeverityType(severity) {
            const typeMap = {
                'critical': 'danger',
                'high': 'danger',
                'medium': 'warning',
                'low': 'info',
                'info': 'info'
            }
            return typeMap[severity] || 'info'
        },

        getSeverityText(severity) {
            const textMap = {
                'critical': '严重',
                'high': '高危',
                'medium': '中危',
                'low': '低危',
                'info': '信息'
            }
            return textMap[severity] || severity
        },

        getToolType(tool) {
            if (tool?.includes('Claude') || tool?.includes('GPT')) {
                return 'danger'
            }
            if (tool === 'CodeBERT') {
                return 'warning'
            }
            if (tool === 'PVS-Studio') {
                return 'primary'
            }
            return 'info'
        },

        getVulnerabilityTitle(vulnerability) {
            let title = vulnerability.type || '漏洞详情'
            if (vulnerability.cwe && vulnerability.cwe !== 'N/A') {
                title += ` (CWE-${vulnerability.cwe})`
            }
            return title
        },

        getDefaultSuggestion(vulnerability) {
            const suggestions = {
                'CWE-120': '使用安全的字符串函数（如 strncpy）替代不安全的函数（如 strcpy），并确保适当的边界检查。',
                'CWE-134': '避免将用户输入直接用作格式化字符串参数，使用固定的格式化字符串。',
                'CWE-787': '在写入缓冲区之前始终检查边界，使用动态内存分配或确保缓冲区足够大。',
                'CWE-125': '在读取数组或缓冲区之前验证索引范围，确保不会越界访问。'
            }
            
            if (vulnerability.cwe && suggestions[`CWE-${vulnerability.cwe}`]) {
                return suggestions[`CWE-${vulnerability.cwe}`]
            }
            
            return '请根据具体情况修复此安全问题，遵循安全编码最佳实践。'
        },

        viewDetail(vulnerability) {
            // 获取代码片段
            const codeSnippet = this.getCodeSnippet(vulnerability)
            
            this.currentVulnerability = {
                ...vulnerability,
                codeSnippet: codeSnippet
            }
            this.detailDialogVisible = true
        },

        getCodeSnippet(vulnerability) {
            // 优先使用行号信息
            if (vulnerability.lineInfo) {
                return `// ${vulnerability.lineInfo}\n// 此处包含 ${vulnerability.type || 'CWE-' + vulnerability.cwe} 漏洞\n// ${vulnerability.message}`
            }
            
            if (vulnerability.startLine && vulnerability.endLine) {
                return `// 第 ${vulnerability.startLine} - ${vulnerability.endLine} 行\n// 此处包含 ${vulnerability.type} 漏洞\n// ${vulnerability.message}`
            }
            
            return `// 漏洞位置\n// ${vulnerability.message}`
        },

        markAsFixed(vulnerability) {
            this.$confirm('确定将此漏洞标记为已修复？', '确认', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'success'
            }).then(() => {
                vulnerability.fixed = true
                this.$message.success('已标记为修复')
                this.$emit('vulnerability-fixed', vulnerability)
            })
        },

        goToCode(vulnerability) {
            this.detailDialogVisible = false
            this.$emit('go-to-code', vulnerability)
        },

        saveResults() {
            const summary = {
                analysisDate: new Date(),
                totalFiles: this.results.length,
                vulnerabilities: {
                    total: this.totalVulnerabilities,
                    high: this.highSeverityCount,
                    medium: this.mediumSeverityCount,
                    low: this.lowSeverityCount,
                    llmConfirmed: this.llmDetectedCount
                },
                details: this.results
            }
            
            this.$emit('save-result', summary)
            this.$message.success('分析结果已保存')
        },

        exportResults() {
            const report = this.generateReport()
            this.$emit('export-result', report)
        },

        generateReport() {
            const cweCounts = {}
            this.vulnerabilities.forEach(vuln => {
                if (vuln.cwe && vuln.cwe !== 'N/A') {
                    cweCounts[vuln.cwe] = (cweCounts[vuln.cwe] || 0) + 1
                }
            })

            return {
                title: '代码漏洞分析报告',
                generatedAt: new Date(),
                summary: {
                    totalFiles: this.results.length,
                    totalVulnerabilities: this.totalVulnerabilities,
                    bySeverity: {
                        high: this.highSeverityCount,
                        medium: this.mediumSeverityCount,
                        low: this.lowSeverityCount
                    },
                    byTool: {
                        llm: this.vulnerabilities.filter(v => v.isLLMConfirmed).length,
                        sast: this.vulnerabilities.filter(v => v.tool === 'PVS-Studio').length,
                        similarity: this.vulnerabilities.filter(v => v.isSimilarityAnalysis).length
                    },
                    topCWEs: Object.entries(cweCounts)
                        .sort(([,a], [,b]) => b - a)
                        .slice(0, 5)
                        .map(([cwe, count]) => ({ cwe: `CWE-${cwe}`, count }))
                },
                details: this.results.map(result => ({
                    file: result.fileName,
                    path: result.filePath,
                    vulnerabilities: (result.vulnerabilities || []).map(v => ({
                        ...v,
                        confirmedByLLM: v.isLLMConfirmed || false
                    }))
                })),
                recommendations: this.generateRecommendations(),
                llmAnalysis: this.results
                    .filter(r => r.llmInsights)
                    .map(r => ({
                        file: r.fileName,
                        insights: r.llmInsights
                    }))
            }
        },

        generateRecommendations() {
            const recommendations = []

            if (this.llmDetectedCount > 0) {
                recommendations.push({
                    priority: 'critical',
                    message: `立即修复 ${this.llmDetectedCount} 个由 AI 确认的漏洞`
                })
            }

            if (this.highSeverityCount > 0) {
                recommendations.push({
                    priority: 'high',
                    message: '优先修复所有高危漏洞，这些漏洞可能导致严重的安全问题'
                })
            }

            // 根据 CWE 类型生成建议
            const cweTypes = [...new Set(this.vulnerabilities.map(v => v.cwe).filter(cwe => cwe && cwe !== 'N/A'))]
            
            if (cweTypes.includes('120') || cweTypes.includes('787')) {
                recommendations.push({
                    priority: 'high',
                    message: '实施严格的边界检查，使用安全的字符串处理函数'
                })
            }

            if (cweTypes.includes('134')) {
                recommendations.push({
                    priority: 'medium',
                    message: '避免使用用户输入作为格式化字符串参数'
                })
            }

            recommendations.push({
                priority: 'medium',
                message: '建立代码审查流程，定期进行安全扫描'
            })

            return recommendations
        }
    }
}
</script>

<style scoped>
.result-display {
    width: 100%;
}

.result-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.result-summary {
    margin-bottom: 30px;
    padding: 20px;
    background-color: #f5f7fa;
    border-radius: 4px;
}

.llm-summary {
    margin: 20px 0;
    padding: 20px;
    background-color: #fef0f0;
    border-radius: 4px;
}

.llm-summary h3 {
    margin-bottom: 15px;
    color: #f56c6c;
}

.filter-bar {
    margin-bottom: 15px;
}

.vulnerability-list {
    margin-top: 30px;
}

.vulnerability-list h3 {
    margin-bottom: 20px;
    color: #303133;
}

.vulnerability-detail {
    padding: 20px;
}

.reasoning-content {
    white-space: pre-wrap;
    background-color: #f5f7fa;
    padding: 10px;
    border-radius: 4px;
    margin-top: 5px;
}

.code-preview {
    margin-top: 20px;
}

.code-preview h4 {
    margin-bottom: 10px;
    color: #303133;
}

.code-preview pre {
    background-color: #f5f7fa;
    padding: 15px;
    border-radius: 4px;
    overflow-x: auto;
}

.code-preview code {
    font-family: 'Consolas', 'Monaco', monospace;
    font-size: 14px;
    color: #303133;
}

.llm-detail {
    margin-top: 20px;
}

.dialog-footer {
    display: flex;
    justify-content: flex-end;
}

.el-tag {
    margin-right: 5px;
}
</style>

