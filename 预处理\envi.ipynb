{"cells": [{"cell_type": "code", "execution_count": 1, "id": "e690b9fe-e383-4897-8276-161acce26b95", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Looking in indexes: http://mirrors.aliyun.com/pypi/simple\n", "Collecting transformers\n", "  Downloading http://mirrors.aliyun.com/pypi/packages/20/37/1f29af63e9c30156a3ed6ebc2754077016577c094f31de7b2631e5d379eb/transformers-4.49.0-py3-none-any.whl (10.0 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m10.0/10.0 MB\u001b[0m \u001b[31m8.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m00:01\u001b[0m0:01\u001b[0mm\n", "\u001b[?25hRequirement already satisfied: tqdm>=4.27 in /root/miniconda3/lib/python3.10/site-packages (from transformers) (4.64.1)\n", "Collecting safetensors>=0.4.1\n", "  Downloading http://mirrors.aliyun.com/pypi/packages/c5/dc/8952caafa9a10a3c0f40fa86bacf3190ae7f55fa5eef87415b97b29cb97f/safetensors-0.5.2-cp38-abi3-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (461 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m462.0/462.0 kB\u001b[0m \u001b[31m21.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting huggingface-hub<1.0,>=0.26.0\n", "  Downloading http://mirrors.aliyun.com/pypi/packages/ea/da/6c2bea5327b640920267d3bf2c9fc114cfbd0a5de234d81cda80cc9e33c8/huggingface_hub-0.28.1-py3-none-any.whl (464 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m464.1/464.1 kB\u001b[0m \u001b[31m68.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: packaging>=20.0 in /root/miniconda3/lib/python3.10/site-packages (from transformers) (23.2)\n", "Requirement already satisfied: requests in /root/miniconda3/lib/python3.10/site-packages (from transformers) (2.31.0)\n", "Requirement already satisfied: filelock in /root/miniconda3/lib/python3.10/site-packages (from transformers) (3.13.1)\n", "Collecting regex!=2019.12.17\n", "  Downloading http://mirrors.aliyun.com/pypi/packages/f2/98/26d3830875b53071f1f0ae6d547f1d98e964dd29ad35cbf94439120bb67a/regex-2024.11.6-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (781 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m781.7/781.7 kB\u001b[0m \u001b[31m29.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: numpy>=1.17 in /root/miniconda3/lib/python3.10/site-packages (from transformers) (1.26.3)\n", "Requirement already satisfied: pyyaml>=5.1 in /root/miniconda3/lib/python3.10/site-packages (from transformers) (6.0.1)\n", "Collecting tokenizers<0.22,>=0.21\n", "  Downloading http://mirrors.aliyun.com/pypi/packages/22/06/69d7ce374747edaf1695a4f61b83570d91cc8bbfc51ccfecf76f56ab4aac/tokenizers-0.21.0-cp39-abi3-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (3.0 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m3.0/3.0 MB\u001b[0m \u001b[31m16.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0ma \u001b[36m0:00:01\u001b[0m\n", "\u001b[?25hRequirement already satisfied: fsspec>=2023.5.0 in /root/miniconda3/lib/python3.10/site-packages (from huggingface-hub<1.0,>=0.26.0->transformers) (2023.12.2)\n", "Requirement already satisfied: typing-extensions>=3.7.4.3 in /root/miniconda3/lib/python3.10/site-packages (from huggingface-hub<1.0,>=0.26.0->transformers) (4.9.0)\n", "Requirement already satisfied: certifi>=2017.4.17 in /root/miniconda3/lib/python3.10/site-packages (from requests->transformers) (2022.12.7)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /root/miniconda3/lib/python3.10/site-packages (from requests->transformers) (1.26.13)\n", "Requirement already satisfied: idna<4,>=2.5 in /root/miniconda3/lib/python3.10/site-packages (from requests->transformers) (3.4)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /root/miniconda3/lib/python3.10/site-packages (from requests->transformers) (2.0.4)\n", "Installing collected packages: safetensors, regex, huggingface-hub, tokenizers, transformers\n", "Successfully installed huggingface-hub-0.28.1 regex-2024.11.6 safetensors-0.5.2 tokenizers-0.21.0 transformers-4.49.0\n", "\u001b[33mWARNING: Running pip as the 'root' user can result in broken permissions and conflicting behaviour with the system package manager. It is recommended to use a virtual environment instead: https://pip.pypa.io/warnings/venv\u001b[0m\u001b[33m\n", "\u001b[0mNote: you may need to restart the kernel to use updated packages.\n"]}], "source": ["pip install transformers"]}, {"cell_type": "code", "execution_count": 8, "id": "897d48cb-8c6b-49b4-98d6-693a168a9210", "metadata": {}, "outputs": [], "source": ["import subprocess\n", "import os\n", "\n", "result = subprocess.run('bash -c \"source /etc/network_turbo && env | grep proxy\"', shell=True, capture_output=True, text=True)\n", "output = result.stdout\n", "for line in output.splitlines():\n", "    if '=' in line:\n", "        var, value = line.split('=', 1)\n", "        os.environ[var] = value"]}, {"cell_type": "code", "execution_count": 9, "id": "29664f86-86d1-4d4a-8f3f-addca047444f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["开始加载 CodeBERT 模型...\n", "下载/加载 CodeBERT 失败： (MaxRetryError(\"HTTPSConnectionPool(host='huggingface.co', port=443): Max retries exceeded with url: /microsoft/codebert-base/resolve/main/tokenizer_config.json (Caused by ProxyError('Cannot connect to proxy.', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7fc5142a24a0>: Failed to establish a new connection: [Errno 111] Connection refused')))\"), '(Request ID: d5785437-8692-4d0f-a546-a4eca9622aad)')\n"]}], "source": ["from transformers import RobertaTokenizer, RobertaModel\n", "from transformers.utils import logging\n", "import os\n", "import tqdm\n", "\n", "# 启用 transformers 的日志级别（可选）\n", "logging.set_verbosity_info()\n", "\n", "# 模型名称\n", "MODEL_NAME = \"microsoft/codebert-base\"\n", "\n", "def download_codebert():\n", "    \"\"\"\n", "    下载 CodeBERT 模型文件，并显示进度条\n", "    \"\"\"\n", "    print(\"开始加载 CodeBERT 模型...\")\n", "    try:\n", "        # 加载 Tokenizer 和 Model，如果模型未下载会自动下载\n", "        tokenizer = RobertaTokenizer.from_pretrained(\n", "            MODEL_NAME, cache_dir=\"./codebert_model\", tqdm_class=tqdm.tqdm\n", "        )\n", "        model = RobertaModel.from_pretrained(\n", "            MODEL_NAME, cache_dir=\"./codebert_model\", tqdm_class=tqdm.tqdm\n", "        )\n", "        print(\"CodeBERT 模型加载完成！\")\n", "        return tokenizer, model\n", "    except Exception as e:\n", "        print(\"下载/加载 CodeBERT 失败：\", str(e))\n", "        return None, None\n", "\n", "# 下载 CodeBERT 模型\n", "tokenizer, model = download_codebert()\n"]}, {"cell_type": "code", "execution_count": 1, "id": "4417a3d7-fcfc-4fbc-a57a-659dc10f4a33", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Looking in indexes: http://mirrors.aliyun.com/pypi/simple\n", "Collecting faiss-cpu\n", "  Downloading http://mirrors.aliyun.com/pypi/packages/aa/40/624f0002bb777e37aac1aadfadec1eb4391be6ad05b7fcfbf66049b99a48/faiss_cpu-1.10.0-cp310-cp310-manylinux_2_28_x86_64.whl (30.7 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m30.7/30.7 MB\u001b[0m \u001b[31m958.4 kB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m00:01\u001b[0m00:01\u001b[0m\n", "\u001b[?25hRequirement already satisfied: numpy<3.0,>=1.25.0 in /root/miniconda3/lib/python3.10/site-packages (from faiss-cpu) (1.26.3)\n", "Requirement already satisfied: packaging in /root/miniconda3/lib/python3.10/site-packages (from faiss-cpu) (23.2)\n", "Installing collected packages: faiss-cpu\n", "Successfully installed faiss-cpu-1.10.0\n", "\u001b[33mWARNING: Running pip as the 'root' user can result in broken permissions and conflicting behaviour with the system package manager. It is recommended to use a virtual environment instead: https://pip.pypa.io/warnings/venv\u001b[0m\u001b[33m\n", "\u001b[0mNote: you may need to restart the kernel to use updated packages.\n"]}], "source": ["pip install faiss-cpu\n"]}, {"cell_type": "code", "execution_count": null, "id": "86dfba5b-7a43-4373-b74c-4da5914830f0", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.8"}}, "nbformat": 4, "nbformat_minor": 5}