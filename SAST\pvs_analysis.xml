<?xml version="1.0"?>
<NewDataSet>
  <PVS-Studio_Analysis_Log>
    <Level>2</Level>
    <ErrorType>warning</ErrorType>
    <ErrorCode>V618</ErrorCode>
    <Message>It's dangerous to call the 'printf' function in such a manner, as the line being passed could contain format specification. The example of the safe code: printf(&quot;%s&quot;, str);</Message>
    <Line>12</Line>
    <File>/root/preprocess/SAST/repo/format_string.c</File>
    <CWECode>CWE-134</CWECode>
    <SAST>CERT-FIO30-C</SAST>
  </PVS-Studio_Analysis_Log>
  <PVS-Studio_Analysis_Log>
    <Level>1</Level>
    <ErrorType>error</ErrorType>
    <ErrorCode>V773</ErrorCode>
    <Message>Visibility scope of the 'buffer' pointer was exited without releasing the memory. A memory leak is possible.</Message>
    <Line>23</Line>
    <File>/root/preprocess/SAST/repo/memory_leak.c</File>
    <Positions>
      <Position lines="23,12">/root/preprocess/SAST/repo/memory_leak.c</Position>
    </Positions>
    <CWECode>CWE-401</CWECode>
    <SAST>CERT-MEM31-C, CERT-MEM51-CPP</SAST>
  </PVS-Studio_Analysis_Log>
  <PVS-Studio_Analysis_Log>
    <Level>1</Level>
    <ErrorType>error</ErrorType>
    <ErrorCode>V595</ErrorCode>
    <Message>The 'str' pointer was utilized before it was verified against nullptr. Check lines: 12, 16.</Message>
    <Line>12</Line>
    <File>/root/preprocess/SAST/repo/null_pointer.c</File>
    <Positions>
      <Position lines="12,16">/root/preprocess/SAST/repo/null_pointer.c</Position>
    </Positions>
    <CWECode>CWE-476</CWECode>
    <SAST>CERT-EXP12-C</SAST>
  </PVS-Studio_Analysis_Log>
</NewDataSet>
