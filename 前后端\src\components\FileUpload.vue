<template>
    
    <div class="file-upload-component">
        <!-- 已存在的仓库列表 -->
        <div class="existing-repositories" v-if="existingRepositories.length > 0">
            <h3 class="section-title">
                <i class="el-icon-folder-opened"></i>
                已存在的代码仓库
            </h3>
            <div class="repo-grid">
                <div
                    v-for="repo in existingRepositories"
                    :key="repo.id"
                    class="repo-card"
                    :class="{ active: currentExtractPath === repo.extractPath }"
                    @click="selectRepository(repo)"
                >
                    <div class="repo-header">
                        <i class="el-icon-folder repo-icon"></i>
                        <span class="repo-name">{{ repo.name }}</span>
                        <el-button
                            type="text"
                            icon="el-icon-delete"
                            class="delete-btn"
                            @click.stop="deleteRepository(repo)"
                        ></el-button>
                    </div>
                    <div class="repo-info">
                        <div class="info-item">
                            <i class="el-icon-document"></i>
                            <span>{{ repo.fileCount }} 个文件</span>
                        </div>
                        <div class="info-item">
                            <i class="el-icon-time"></i>
                            <span>{{ formatDate(repo.uploadTime) }}</span>
                        </div>
                        <div class="info-item">
                            <i class="el-icon-files"></i>
                            <span>{{ formatFileSize(repo.size) }}</span>
                        </div>
                    </div>
                    <div class="repo-actions">
                        <el-button
                            size="mini"
                            type="primary"
                            plain
                            round
                            @click.stop="selectRepository(repo)"
                        >
                            {{ currentExtractPath === repo.extractPath ? '已选择' : '选择' }}
                        </el-button>
                    </div>
                </div>
            </div>
        </div>

        <el-divider v-if="existingRepositories.length > 0"></el-divider>

        
        <div class="upload-container">
            <el-upload
                    class="upload-area"
                    drag
                    action="#"
                    :before-upload="beforeUpload"
                    :on-success="handleSuccess"
                    :on-error="handleError"
                    :on-change="handleChange"
                    :file-list="fileList"
                    :auto-upload="false"
                    ref="upload"
            >
                <div class="upload-content">
                    <i class="el-icon-upload upload-icon"></i>
                    <div class="upload-text">
                        <h3>拖拽文件到此处上传</h3>
                        <p>或<em>点击选择文件</em></p>
                    </div>
                    <div class="upload-tip">
                        支持 zip、tar.gz 格式 • 最大 100MB
                    </div>
                </div>
            </el-upload>

            <div class="action-panel">
                <div class="status-info" v-if="currentExtractPath">
                    <i class="el-icon-folder-opened"></i>
                    <span>已加载仓库</span>
                </div>
                
                <div class="button-group">
                    <el-button
                            type="primary"
                            @click="submitUpload"
                            :loading="uploading"
                            :disabled="fileList.length === 0"
                            icon="el-icon-upload2"
                            round
                    >
                        {{ uploading ? '上传中...' : '开始上传' }}
                    </el-button>
                    
                    <el-button
                            type="success"
                            @click="initializeRepository"
                            :loading="initializing"
                            :disabled="!currentExtractPath"
                            icon="el-icon-s-operation"
                            round
                    >
                        {{ initializing ? '初始化中...' : '初始化仓库' }}
                    </el-button>
                    
                    <el-button
                            type="info"
                            @click="openNeo4j"
                            icon="el-icon-view"
                            round
                    >
                        查看 Neo4j
                    </el-button>
                </div>
            </div>
        </div>

        <!-- 文件列表预览 -->
        <div class="file-preview" v-if="fileList.length > 0">
            <h4 class="preview-title">待上传文件</h4>
            <div class="file-item" v-for="file in fileList" :key="file.uid">
                <i class="el-icon-document file-icon"></i>
                <span class="file-name">{{ file.name }}</span>
                <span class="file-size">{{ formatFileSize(file.size) }}</span>
                <i class="el-icon-close remove-icon" @click="removeFile(file)"></i>
            </div>
        </div>
    </div>
</template>

<script>
import JSZip from 'jszip'
import api from '@/api'

export default {
    name: 'FileUpload',
    data() {
        return {
             fileList: [],
            uploading: false,
            initializing: false,
            currentExtractPath: '', // 保存当前的解压路径
            existingRepositories: [] // 已存在的仓库列表
        }
    },
    mounted() {
        // 组件挂载时加载已存在的仓库
        this.loadExistingRepositories()
    },
    methods: {
        async loadExistingRepositories() {
            try {
                const response = await api.getRepositories()
                
                if (response.success) {
                    this.existingRepositories = response.data
                    
                    // 如果有保存的最后选择，自动选择
                    const lastSelected = localStorage.getItem('lastSelectedRepository')
                    if (lastSelected) {
                        const repo = this.existingRepositories.find(r => r.name === lastSelected)
                        if (repo) {
                            this.selectRepository(repo)
                        }
                    }
                }
            } catch (error) {
                console.error('加载仓库列表失败:', error)
            }
        },
        
        // 选择仓库
        selectRepository(repository) {
            this.currentExtractPath = repository.extractPath
            
            // 保存最后选择的仓库
            localStorage.setItem('lastSelectedRepository', repository.name)
            
            // 通知父组件
            this.$emit('repository-selected', repository)
            
            this.$message.success(`已选择仓库: ${repository.name}`)
        },
        
        // 删除仓库
        async deleteRepository(repository) {
            try {
                await this.$confirm(
                    `确定要删除仓库 "${repository.name}" 吗？此操作不可恢复。`,
                    '删除确认',
                    {
                        confirmButtonText: '确定删除',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }
                )
                
                const response = await api.deleteRepository(repository.name)
                
                if (response.success) {
                    this.$message.success('仓库删除成功')
                    
                    // 如果删除的是当前选中的仓库，清空选择
                    if (this.currentExtractPath === repository.extractPath) {
                        this.currentExtractPath = ''
                        localStorage.removeItem('lastSelectedRepository')
                    }
                    
                    // 重新加载列表
                    await this.loadExistingRepositories()
                } else {
                    throw new Error(response.error || '删除失败')
                }
            } catch (error) {
                if (error !== 'cancel') {
                    this.$message.error('删除仓库失败: ' + error.message)
                }
            }
        },
        
        // 格式化日期
        formatDate(date) {
            const d = new Date(date)
            return d.toLocaleDateString() + ' ' + d.toLocaleTimeString()
        },
        // 格式化文件大小
        formatFileSize(size) {
            if (size < 1024) {
                return size + ' B'
            } else if (size < 1024 * 1024) {
                return (size / 1024).toFixed(1) + ' KB'
            } else {
                return (size / (1024 * 1024)).toFixed(1) + ' MB'
            }
        },

        // 移除文件
        removeFile(file) {
            const index = this.fileList.findIndex(f => f.uid === file.uid)
            if (index > -1) {
                this.fileList.splice(index, 1)
                this.$refs.upload.handleRemove(file)
            }
        },

        // 打开 Neo4j 浏览器
        openNeo4j() {
            window.open('http://localhost:7474/browser/', '_blank')
        },
        
        beforeUpload(file) {
            const isZip = file.type === 'application/zip' || 
                         file.type === 'application/x-zip-compressed' ||
                         file.name.endsWith('.zip')
            const isTarGz = file.type === 'application/x-gzip' || 
                           file.name.endsWith('.tar.gz') ||
                           file.name.endsWith('.tgz')
            const isValidType = isZip || isTarGz
            const isLt100M = file.size / 1024 / 1024 < 100

            if (!isValidType) {
                this.$message.error('只支持 zip、tar.gz 格式的文件！')
                return false
            }

            if (!isLt100M) {
                this.$message.error('文件大小不能超过 100MB！')
                return false
            }

            return true
        },

        async submitUpload() {
            if (this.fileList.length === 0) {
                this.$message.warning('请先选择要上传的文件')
                return
            }

            this.uploading = true

            try {
                const file = this.fileList[0].raw
                const response = await api.uploadAndExtract(file)

                if (response.success) {
                    this.currentExtractPath = response.data.extractPath
                    
                    const repository = {
                        id: Date.now(),
                        name: response.data.originalName,
                        uploadTime: new Date(),
                        fileCount: response.data.fileCount,
                        size: response.data.size,
                        files: response.data.files,
                        extractPath: response.data.extractPath
                    }

                    this.$emit('upload-success', repository)
                    this.fileList = []
                    this.$refs.upload.clearFiles()
                    this.$message.success('文件上传并解压成功！')
                    
                    // 刷新仓库列表
                    await this.loadExistingRepositories()
                } else {
                    throw new Error(response.error || '上传失败')
                }
            } catch (error) {
                this.$message.error('处理失败：' + error.message)
            } finally {
                this.uploading = false
            }
        },
        
        async initializeRepository() {
            if (!this.currentExtractPath) {
                this.$message.warning('请先上传代码仓库')
                return
            }

            this.initializing = true

            try {
                const response = await api.analyzeRepository(this.currentExtractPath)

                if (response.success) {
                    this.$message.success('仓库初始化成功！')
                    // 可以在这里处理返回的分析结果
                    this.$emit('repository-initialized', response.data)
                } else {
                    throw new Error(response.error || '初始化失败')
                }
            } catch (error) {
                this.$message.error('仓库初始化失败：' + error.message)
            } finally {
                this.initializing = false
            }
        },

        async parseZipFile(file) {
            const zip = new JSZip()
            const contents = await zip.loadAsync(file)
            const fileStructure = []
            const folderMap = new Map()
            let fileCount = 0

            // 构建文件树结构
            for (const [path, zipEntry] of Object.entries(contents.files)) {
                if (!zipEntry.dir) {
                    fileCount++
                }

                const pathParts = path.split('/')
                let currentLevel = fileStructure
                let currentPath = ''

                for (let i = 0; i < pathParts.length; i++) {
                    const part = pathParts[i]
                    if (!part) continue

                    currentPath += (currentPath ? '/' : '') + part
                    const isFile = i === pathParts.length - 1 && !zipEntry.dir

                    if (isFile) {
                        // 添加文件
                        currentLevel.push({
                            id: path,
                            name: part,
                            type: 'file',
                            path: '/' + path,
                            size: zipEntry._data ? zipEntry._data.uncompressedSize : 0,
                            zipEntry: zipEntry // 保存 zip 条目以便后续读取内容
                        })
                    } else {
                        // 处理文件夹
                        let folder = folderMap.get(currentPath)
                        
                        if (!folder) {
                            folder = {
                                id: currentPath,
                                name: part,
                                type: 'folder',
                                path: '/' + currentPath,
                                children: []
                            }
                            currentLevel.push(folder)
                            folderMap.set(currentPath, folder)
                        }
                        
                        currentLevel = folder.children
                    }
                }
            }

            return {
                structure: fileStructure,
                fileCount: fileCount
            }
        },

        handleSuccess(response, file, fileList) {
            this.fileList = fileList
        },

        handleChange(file, fileList) {
            console.log('File changed:', file.name, 'Total files:', fileList.length)
            this.fileList = fileList
        },

        handleError(err, file) {
            this.$message.error('文件上传失败：' + err.message)
        }
    }
}
</script>

<style scoped>
.file-upload-component {
    padding: 30px;
    background-color: #f5f7fa;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
}

.upload-container {
    background: white;
    border-radius: 12px;
    padding: 40px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

/* 上传区域样式 */
.upload-area {
    width: 100%;
}

.upload-area ::v-deep .el-upload-dragger {
    width: 100%;
    height: 280px;
    border: 2px dashed #d9d9d9;
    border-radius: 12px;
    background-color: #fafbfc;
    transition: all 0.3s ease;
}

.upload-area ::v-deep .el-upload-dragger:hover {
    border-color: #409eff;
    background-color: #f0f7ff;
}

.upload-content {
    padding: 40px;
    text-align: center;
}

.upload-icon {
    font-size: 72px;
    color: #409eff;
    margin-bottom: 20px;
    display: block;
}

.upload-text h3 {
    margin: 0 0 12px 0;
    font-size: 20px;
    font-weight: 500;
    color: #303133;
}

.upload-text p {
    margin: 0;
    font-size: 14px;
    color: #606266;
}

.upload-text em {
    color: #409eff;
    font-style: normal;
    cursor: pointer;
    font-weight: 500;
}

.upload-tip {
    margin-top: 20px;
    font-size: 12px;
    color: #909399;
    background-color: #f4f4f5;
    padding: 8px 16px;
    border-radius: 20px;
    display: inline-block;
}

/* 操作面板样式 */
.action-panel {
    margin-top: 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 30px;
    border-top: 1px solid #ebeef5;
}

.status-info {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #67c23a;
    font-size: 14px;
}

.status-info i {
    font-size: 18px;
}

.button-group {
    display: flex;
    gap: 12px;
}

.button-group .el-button {
    margin: 0;
    padding: 12px 24px;
    font-weight: 500;
}

/* 文件预览样式 */
.file-preview {
    margin-top: 20px;
    background: white;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.preview-title {
    margin: 0 0 16px 0;
    font-size: 16px;
    color: #303133;
    font-weight: 500;
}

.file-item {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    background-color: #f5f7fa;
    border-radius: 8px;
    margin-bottom: 8px;
    transition: all 0.3s ease;
}

.file-item:hover {
    background-color: #e6f0ff;
}

.file-icon {
    font-size: 20px;
    color: #409eff;
    margin-right: 12px;
}

.file-name {
    flex: 1;
    font-size: 14px;
    color: #303133;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.file-size {
    font-size: 12px;
    color: #909399;
    margin: 0 16px;
}

.remove-icon {
    font-size: 16px;
    color: #909399;
    cursor: pointer;
    transition: color 0.3s ease;
}

.remove-icon:hover {
    color: #f56c6c;
}
.existing-repositories {
    margin-bottom: 30px;
}

.section-title {
    display: flex;
    align-items: center;
    gap: 8px;
    margin: 0 0 20px 0;
    font-size: 18px;
    color: #303133;
}

.repo-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
}

.repo-card {
    background: white;
    border: 2px solid #e4e7ed;
    border-radius: 12px;
    padding: 20px;
    transition: all 0.3s ease;
    cursor: pointer;
}

.repo-card:hover {
    border-color: #409eff;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.repo-card.active {
    border-color: #409eff;
    background-color: #f0f7ff;
}

.repo-header {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

.repo-icon {
    font-size: 24px;
    color: #409eff;
    margin-right: 10px;
}

.repo-name {
    flex: 1;
    font-size: 16px;
    font-weight: 500;
    color: #303133;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.delete-btn {
    padding: 0;
    font-size: 18px;
    color: #909399;
}

.delete-btn:hover {
    color: #f56c6c;
}

.repo-info {
    margin-bottom: 15px;
}

.info-item {
    display: flex;
    align-items: center;
    gap: 6px;
    margin-bottom: 8px;
    font-size: 13px;
    color: #606266;
}

.info-item i {
    font-size: 14px;
    color: #909399;
}

.repo-actions {
    text-align: right;
}

.el-divider {
    margin: 30px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .upload-container {
        padding: 20px;
    }
    
    .action-panel {
        flex-direction: column;
        gap: 20px;
    }
    
    .button-group {
        flex-wrap: wrap;
        justify-content: center;
    }
}
</style>
