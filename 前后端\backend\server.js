const express = require('express');
const multer = require('multer');
const unzipper = require('unzipper');
const cors = require('cors');
const path = require('path');
const fs = require('fs');
const { exec } = require('child_process');
const util = require('util');
const execPromise = util.promisify(exec);

const app = express();
const PORT = 3001;

// 启用 CORS
app.use(cors());
app.use(express.json());

// 确保上传目录存在
const UPLOAD_DIR = path.join(__dirname, 'uploads');
const EXTRACT_DIR = path.join(__dirname, 'extracted');

if (!fs.existsSync(UPLOAD_DIR)) {
    fs.mkdirSync(UPLOAD_DIR, { recursive: true });
}
if (!fs.existsSync(EXTRACT_DIR)) {
    fs.mkdirSync(EXTRACT_DIR, { recursive: true });
}

// 配置文件上传
const storage = multer.diskStorage({
    destination: function (req, file, cb) {
        cb(null, UPLOAD_DIR)
    },
    filename: function (req, file, cb) {
        // 使用时间戳避免文件名冲突
        const uniqueName = `${Date.now()}-${file.originalname}`;
        cb(null, uniqueName)
    }
});

const upload = multer({
    storage: storage,
    limits: {
        fileSize: 100 * 1024 * 1024 // 100MB
    }
});

// 上传并解压文件
app.post('/api/upload', upload.single('file'), async (req, res) => {
    try {
        if (!req.file) {
            return res.status(400).json({ error: '没有文件上传' });
        }

        const uploadedFile = req.file;
        const extractPath = path.join(EXTRACT_DIR, `${path.basename(uploadedFile.originalname, '.zip')}`);

        // 创建解压目录
        if (!fs.existsSync(extractPath)) {
            fs.mkdirSync(extractPath, { recursive: true });
        }

        console.log(`开始解压文件: ${uploadedFile.path} 到 ${extractPath}`);

        // 解压文件
        await new Promise((resolve, reject) => {
            fs.createReadStream(uploadedFile.path)
                .pipe(unzipper.Extract({ path: extractPath }))
                .on('close', resolve)
                .on('error', reject);
        });

        console.log('解压完成');

        // 获取解压后的文件列表
        const files = await getFileTree(extractPath);

        // 返回结果
        res.json({
            success: true,
            message: '文件上传并解压成功',
            data: {
                originalName: uploadedFile.originalname,
                uploadPath: uploadedFile.path,
                extractPath: extractPath,
                files: files,
                fileCount: countFiles(files),
                size: uploadedFile.size
            }
        });

        // 可选：删除原始上传的 ZIP 文件
        // fs.unlinkSync(uploadedFile.path);

    } catch (error) {
        console.error('处理文件时出错:', error);
        res.status(500).json({
            success: false,
            error: '文件处理失败',
            details: error.message
        });
    }
});
// Add a new endpoint specifically for SAST analysis
app.post('/api/SASTanalyze', async (req, res) => {
    try {
        const { extractPath, filePath, analysisType } = req.body;

        if (!extractPath || !fs.existsSync(extractPath)) {
            return res.status(400).json({ 
                success: false,
                error: '无效的项目路径' 
            });
        }

        const fullFilePath = path.join(extractPath, filePath);
        
        // Check if it's a C/C++ file
        const fileExt = path.extname(filePath).toLowerCase();
        const cppExtensions = ['.c', '.cpp', '.cc', '.cxx', '.h', '.hpp'];
        
        if (cppExtensions.includes(fileExt)) {
            // Run SAST analysis for C/C++ files
            const sastScript = path.join(__dirname, 'analyzers', 'SAST.py');
            const command = `python3 "${sastScript}" "${fullFilePath}"`;
            
            console.log('执行SAST分析:', command);
            
            try {
                const { stdout, stderr } = await execPromise(command, {
                    maxBuffer: 10 * 1024 * 1024,
                    timeout: 60000 // 60 seconds timeout
                });

                if (stderr) {
                    console.error('SAST stderr:', stderr);
                }

                const analysisResult = JSON.parse(stdout);
                
                res.json({
                    success: true,
                    data: analysisResult
                });
                
            } catch (execError) {
                console.error('SAST执行错误:', execError);
                res.json({
                    success: false,
                    error: 'SAST分析失败',
                    data: { vulnerabilities: [] }
                });
            }
        } else {
            // For other file types, return empty result or use different analyzer
            res.json({
                success: true,
                data: {
                    vulnerabilities: [],
                    message: 'SAST only supports C/C++ files'
                }
            });
        }

    } catch (error) {
        console.error('分析失败:', error);
        res.status(500).json({
            success: false,
            error: '代码分析失败',
            details: error.message
        });
    }
});

// 分析代码漏洞的端点

app.post('/api/feature-extraction', async (req, res) => {
    try {
        const { code, extractPath } = req.body;

        if (!code) {
            return res.status(400).json({ 
                success: false,
                error: '没有提供代码内容' 
            });
        }

        // 创建临时文件存储代码
        const tempCodeFile = path.join(UPLOAD_DIR, `temp_${Date.now()}.txt`);
        fs.writeFileSync(tempCodeFile, code, 'utf8');

        // 调用 Python 脚本进行特征提取
        const pythonScript = path.join(__dirname, 'analyzers', 'feature_extractor.py');
        const command = `python3 "${pythonScript}" "${tempCodeFile}"`;
        
        console.log('执行特征提取:', command);

        const { stdout, stderr } = await execPromise(command, {
            maxBuffer: 10 * 1024 * 1024,
            timeout: 30000 // 30秒超时
        });

        // 清理临时文件
        fs.unlinkSync(tempCodeFile);

        if (stderr) {
            console.error('特征提取 stderr:', stderr);
        }

        const result = JSON.parse(stdout);
        
        res.json({
            success: true,
            data: result
        });

    } catch (error) {
        console.error('特征提取失败:', error);
        res.status(500).json({
            success: false,
            error: '特征提取失败',
            details: error.message
        });
    }
});

// 读取文件内容
app.get('/api/file-content', (req, res) => {
    try {
        const { path: filePath } = req.query;

        if (!filePath || !fs.existsSync(filePath)) {
            return res.status(404).json({ error: '文件不存在' });
        }

        const content = fs.readFileSync(filePath, 'utf8');
        res.json({
            success: true,
            content: content
        });

    } catch (error) {
        res.status(500).json({
            success: false,
            error: '读取文件失败',
            details: error.message
        });
    }
});

// 清理临时文件
app.post('/api/cleanup', (req, res) => {
    try {
        const { extractPath } = req.body;

        if (extractPath && fs.existsSync(extractPath)) {
            fs.rmSync(extractPath, { recursive: true, force: true });
        }

        res.json({ success: true, message: '清理完成' });
    } catch (error) {
        res.status(500).json({
            success: false,
            error: '清理失败',
            details: error.message
        });
    }
});

// 综合分析文件（包含 SAST、CWE 和依赖分析）
// 综合分析文件（包含 SAST、CWE 和依赖分析）

app.post('/api/analyze-llm', async (req, res) => {
    const { repoPath, filePath, action } = req.body;

    try {
        const result = await runPythonScript('Server.py', [
            action || 'prompt',
            filePath,
            '--source-dir', repoPath
        ]);

        res.json({
            success: true,
            data: result
        });
    } catch (error) {
        console.error('LLM analysis error:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});
// 修改 /api/analyze-comprehensive 端点
// 综合分析文件（包含 SAST、CWE 和依赖分析）
app.post('/api/analyze-comprehensive', async (req, res) => {
    try {
        const { filePath, extractPath, analysisTypes = ['all'] } = req.body;

        if (!filePath || !extractPath) {
            return res.status(400).json({
                success: false,
                error: '缺少必要参数'
            });
        }

        const fullFilePath = path.join(extractPath, filePath);

        if (!fs.existsSync(fullFilePath)) {
            return res.status(404).json({
                success: false,
                error: '文件不存在'
            });
        }

        // 使用 Server.py 的 prompt 命令来执行综合分析并获取 LLM 响应
        const analyzerScript = '/root/preprocess/Server.py';
        const command = `python3 "${analyzerScript}" prompt "${fullFilePath}" --source-dir "${extractPath}"`;

        console.log('执行综合分析并生成LLM响应:', command);

        const { stdout, stderr } = await execPromise(command, {
            maxBuffer: 50 * 1024 * 1024,
            timeout: 180000 // 3分钟超时
        });

        if (stderr && !stderr.includes('WARNING')) {
            console.error('综合分析 stderr:', stderr);
        }

        // 由于 stdout 可能包含非 JSON 内容，我们需要提取 JSON 部分
        let promptResult = null;
        
        try {
            // 尝试找到 JSON 输出
            const lines = stdout.split('\n');
            let jsonStr = '';
            let inJson = false;
            let braceCount = 0;
            
            for (const line of lines) {
                if (line.trim().startsWith('{') && !inJson) {
                    inJson = true;
                    jsonStr = '';
                    braceCount = 0;
                }
                
                if (inJson) {
                    jsonStr += line + '\n';
                    braceCount += (line.match(/\{/g) || []).length;
                    braceCount -= (line.match(/\}/g) || []).length;
                    
                    if (braceCount === 0 && jsonStr.trim()) {
                        try {
                            promptResult = JSON.parse(jsonStr.trim());
                            if (promptResult.success !== undefined) {
                                break;
                            }
                        } catch (e) {
                            // 继续尝试
                        }
                    }
                }
            }
            
            if (!promptResult) {
                // 如果无法从 stdout 解析，尝试直接解析最后的有效 JSON
                const jsonMatch = stdout.match(/\{[^{}]*"success"[^{}]*\}(?!.*\{.*"success")/s);
                if (jsonMatch) {
                    promptResult = JSON.parse(jsonMatch[0]);
                }
            }
        } catch (parseError) {
            console.error('解析 stdout 失败:', parseError);
        }

        // 如果仍然无法解析 stdout，尝试查找生成的文件
        if (!promptResult || !promptResult.llm_response_path) {
            // 基于文件名查找最新的响应文件
            const baseName = path.basename(filePath, path.extname(filePath));
            const responseDir = '/root/preprocess/testrepos/llm_responses';
            
            if (fs.existsSync(responseDir)) {
                const files = fs.readdirSync(responseDir);
                const responseFiles = files.filter(f => 
                    f.startsWith(baseName) && f.endsWith('_llm_response.json')
                ).sort().reverse(); // 按时间倒序
                
                if (responseFiles.length > 0) {
                    const latestResponseFile = path.join(responseDir, responseFiles[0]);
                    try {
                        const responseData = JSON.parse(fs.readFileSync(latestResponseFile, 'utf8'));
                        
                        promptResult = {
                            success: true,
                            llm_response_path: latestResponseFile,
                            llm_response: responseData.raw_response,
                            detected_vulnerabilities: responseData.parsed_vulnerabilities || [],
                            detected_cwes: responseData.detected_cwes || [],
                            vulnerability_count: responseData.vulnerability_count || 0
                        };
                    } catch (e) {
                        console.error('读取响应文件失败:', e);
                    }
                }
            }
        }

        if (!promptResult) {
            return res.status(500).json({
                success: false,
                error: '无法解析分析结果',
                details: '分析可能已完成，但无法读取结果'
            });
        }

        // 如果有 LLM 响应文件路径，尝试读取详细内容
        if (promptResult.llm_response_path && fs.existsSync(promptResult.llm_response_path)) {
            try {
                const llmResponseContent = fs.readFileSync(promptResult.llm_response_path, 'utf8');
                const llmResponseData = JSON.parse(llmResponseContent);
                
                // 确保包含所有必要的数据
                promptResult.llm_response = promptResult.llm_response || llmResponseData.raw_response;
                promptResult.detected_vulnerabilities = promptResult.detected_vulnerabilities || llmResponseData.parsed_vulnerabilities || [];
                promptResult.detected_cwes = promptResult.detected_cwes || llmResponseData.detected_cwes || [];
                promptResult.vulnerability_count = promptResult.vulnerability_count || llmResponseData.vulnerability_count || 0;
                promptResult.llm_model = llmResponseData.llm_model;
            } catch (e) {
                console.error('读取LLM响应文件失败:', e);
            }
        }

        // 构建最终响应
        const responseData = {
            success: true,
            filePath: filePath,
            llm_response: promptResult.llm_response,
            detected_vulnerabilities: promptResult.detected_vulnerabilities || [],
            detected_cwes: promptResult.detected_cwes || [],
            vulnerability_count: promptResult.vulnerability_count || 0,
            prompt_path: promptResult.prompt_path,
            llm_response_path: promptResult.llm_response_path,
            timestamp: promptResult.timestamp || new Date().toISOString(),
            error: promptResult.llm_error
        };

        res.json({
            success: true,
            data: responseData
        });

    } catch (error) {
        console.error('综合分析失败:', error);
        res.status(500).json({
            success: false,
            error: '综合分析失败',
            details: error.message
        });
    }
});
// 获取 LLM 响应文件列表
app.get('/api/llm-response-files', async (req, res) => {
    try {
        const { extractPath, fileName } = req.query;
        
        // 构建响应文件目录路径
        const responseDir = path.join("/root/preprocess/testrepos", 'llm_responses');
        
        if (!fs.existsSync(responseDir)) {
            return res.json({
                success: true,
                data: { files: [] }
            });
        }

        // 读取目录中的文件
        const files = fs.readdirSync(responseDir);
        
        // 过滤出与当前文件相关的响应文件
        const fileBaseName = path.basename(fileName, path.extname(fileName));
        const relevantFiles = files
            .filter(f => f.includes(fileBaseName) && f.endsWith('_llm_response.json'))
            .map(f => {
                const filePath = path.join(responseDir, f);
                const stat = fs.statSync(filePath);
                
                // 从文件名中提取时间戳
                const timestampMatch = f.match(/(\d{8}_\d{6})/);
                let timestamp = stat.mtime;
                
                if (timestampMatch) {
                    const [date, time] = timestampMatch[1].split('_');
                    const year = date.substring(0, 4);
                    const month = date.substring(4, 6);
                    const day = date.substring(6, 8);
                    const hour = time.substring(0, 2);
                    const minute = time.substring(2, 4);
                    const second = time.substring(4, 6);
                    
                    timestamp = new Date(`${year}-${month}-${day}T${hour}:${minute}:${second}`);
                }
                
                return {
                    name: f,
                    path: filePath,
                    size: stat.size,
                    timestamp: timestamp
                };
            })
            .sort((a, b) => b.timestamp - a.timestamp); // 按时间降序排列

        res.json({
            success: true,
            data: { files: relevantFiles }
        });

    } catch (error) {
        console.error('获取响应文件列表失败:', error);
        res.status(500).json({
            success: false,
            error: '获取响应文件列表失败',
            details: error.message
        });
    }
});

// 加载 LLM 响应文件内容
app.get('/api/load-llm-response', async (req, res) => {
    try {
        const { path: filePath } = req.query;

        if (!filePath || !fs.existsSync(filePath)) {
            return res.status(404).json({
                success: false,
                error: '文件不存在'
            });
        }

        const content = fs.readFileSync(filePath, 'utf8');
        const data = JSON.parse(content);

        res.json({
            success: true,
            data: data
        });

    } catch (error) {
        console.error('加载响应文件失败:', error);
        res.status(500).json({
            success: false,
            error: '加载响应文件失败',
            details: error.message
        });
    }
});
// 在 server.js 中添加以下端点

// 获取已存在的仓库列表
app.get('/api/repositories', async (req, res) => {
    try {
        if (!fs.existsSync(EXTRACT_DIR)) {
            return res.json({
                success: true,
                data: []
            });
        }

        const directories = fs.readdirSync(EXTRACT_DIR);
        const repositories = [];

        for (const dir of directories) {
            const dirPath = path.join(EXTRACT_DIR, dir);
            const stat = fs.statSync(dirPath);

            if (stat.isDirectory()) {
                try {
                    // 获取文件树
                    const files = await getFileTree(dirPath);
                    const fileCount = countFiles(files);

                    // 获取目录大小
                    const size = await getDirectorySize(dirPath);

                    repositories.push({
                        id: Date.now() + Math.random(),
                        name: dir,
                        extractPath: dirPath,
                        uploadTime: stat.mtime,
                        fileCount: fileCount,
                        size: size,
                        files: files
                    });
                } catch (error) {
                    console.error(`获取仓库信息失败 ${dir}:`, error);
                }
            }
        }

        // 按修改时间排序
        repositories.sort((a, b) => b.uploadTime - a.uploadTime);

        res.json({
            success: true,
            data: repositories
        });

    } catch (error) {
        console.error('获取仓库列表失败:', error);
        res.status(500).json({
            success: false,
            error: '获取仓库列表失败',
            details: error.message
        });
    }
});

// 获取特定仓库的信息
app.get('/api/repository/:name', async (req, res) => {
    try {
        const { name } = req.params;
        const dirPath = path.join(EXTRACT_DIR, name);

        if (!fs.existsSync(dirPath)) {
            return res.status(404).json({
                success: false,
                error: '仓库不存在'
            });
        }

        const stat = fs.statSync(dirPath);
        const files = await getFileTree(dirPath);
        const fileCount = countFiles(files);
        const size = await getDirectorySize(dirPath);

        const repository = {
            id: Date.now(),
            name: name,
            extractPath: dirPath,
            uploadTime: stat.mtime,
            fileCount: fileCount,
            size: size,
            files: files
        };

        res.json({
            success: true,
            data: repository
        });

    } catch (error) {
        console.error('获取仓库信息失败:', error);
        res.status(500).json({
            success: false,
            error: '获取仓库信息失败',
            details: error.message
        });
    }
});

// 辅助函数：计算目录大小
async function getDirectorySize(dir) {
    let size = 0;
    
    async function calculateSize(dirPath) {
        const files = fs.readdirSync(dirPath);
        
        for (const file of files) {
            const filePath = path.join(dirPath, file);
            const stat = fs.statSync(filePath);
            
            if (stat.isDirectory()) {
                await calculateSize(filePath);
            } else {
                size += stat.size;
            }
        }
    }
    
    await calculateSize(dir);
    return size;
}

// 删除仓库
app.delete('/api/repository/:name', async (req, res) => {
    try {
        const { name } = req.params;
        const dirPath = path.join(EXTRACT_DIR, name);

        if (!fs.existsSync(dirPath)) {
            return res.status(404).json({
                success: false,
                error: '仓库不存在'
            });
        }

        // 删除目录
        fs.rmSync(dirPath, { recursive: true, force: true });

        res.json({
            success: true,
            message: '仓库删除成功'
        });

    } catch (error) {
        console.error('删除仓库失败:', error);
        res.status(500).json({
            success: false,
            error: '删除仓库失败',
            details: error.message
        });
    }
});



// 仅进行 SAST 分析
app.post('/api/analyze-sast-only', async (req, res) => {
    try {
        const { filePath, extractPath } = req.body;

        if (!filePath || !extractPath) {
            return res.status(400).json({
                success: false,
                error: '缺少必要参数'
            });
        }

        const fullFilePath = path.join(extractPath, filePath);

        const analyzerScript = path.join(__dirname, 'analyzers', 'integrated_analyzer.py');
        const command = `python3 "${analyzerScript}" sast "${fullFilePath}" --source-dir "${extractPath}"`;

        console.log('执行 SAST 分析:', command);

        const { stdout, stderr } = await execPromise(command, {
            maxBuffer: 20 * 1024 * 1024,
            timeout: 60000
        });

        if (stderr && !stderr.includes('WARNING')) {
            console.error('SAST 分析 stderr:', stderr);
        }

        const analysisResult = JSON.parse(stdout);

        res.json({
            success: true,
            data: analysisResult
        });

    } catch (error) {
        console.error('SAST 分析失败:', error);
        res.status(500).json({
            success: false,
            error: 'SAST 分析失败',
            details: error.message
        });
    }
});

// CWE 相似性搜索
app.post('/api/analyze-cwe', async (req, res) => {
    try {
        const { filePath, extractPath, code } = req.body;

        if (!filePath && !code) {
            return res.status(400).json({
                success: false,
                error: '需要提供文件路径或代码内容'
            });
        }

        let fullFilePath;
        if (filePath && extractPath) {
            fullFilePath = path.join(extractPath, filePath);
        } else if (code) {
            // 如果直接提供代码，创建临时文件
            fullFilePath = path.join(UPLOAD_DIR, `temp_cwe_${Date.now()}.c`);
            fs.writeFileSync(fullFilePath, code, 'utf8');
        }

        const analyzerScript = path.join(__dirname, 'analyzers', 'integrated_analyzer.py');
        const command = `python3 "${analyzerScript}" cwe "${fullFilePath}"`;

        console.log('执行 CWE 分析:', command);

        const { stdout, stderr } = await execPromise(command, {
            maxBuffer: 20 * 1024 * 1024,
            timeout: 60000
        });

        // 清理临时文件
        if (code && fs.existsSync(fullFilePath)) {
            fs.unlinkSync(fullFilePath);
        }

        if (stderr && !stderr.includes('WARNING')) {
            console.error('CWE 分析 stderr:', stderr);
        }

        const analysisResult = JSON.parse(stdout);

        res.json({
            success: true,
            data: analysisResult
        });

    } catch (error) {
        console.error('CWE 分析失败:', error);
        res.status(500).json({
            success: false,
            error: 'CWE 分析失败',
            details: error.message
        });
    }
});

// 依赖分析
app.post('/api/analyze-dependencies', async (req, res) => {
    try {
        const { filePath, extractPath } = req.body;

        if (!filePath || !extractPath) {
            return res.status(400).json({
                success: false,
                error: '缺少必要参数'
            });
        }

        const fullFilePath = path.join(extractPath, filePath);

        const analyzerScript = path.join(__dirname, 'analyzers', 'integrated_analyzer.py');
        const command = `python3 "${analyzerScript}" dependencies "${fullFilePath}"`;

        console.log('执行依赖分析:', command);

        const { stdout, stderr } = await execPromise(command, {
            maxBuffer: 20 * 1024 * 1024,
            timeout: 60000
        });

        if (stderr && !stderr.includes('WARNING')) {
            console.error('依赖分析 stderr:', stderr);
        }

        const analysisResult = JSON.parse(stdout);

        res.json({
            success: true,
            data: analysisResult
        });

    } catch (error) {
        console.error('依赖分析失败:', error);
        res.status(500).json({
            success: false,
            error: '依赖分析失败',
            details: error.message
        });
    }
});

// 分析整个仓库
// // 分析整个仓库
app.post('/api/analyze-repository', async (req, res) => {
    try {
        const { extractPath, clearDb = true } = req.body;

        if (!extractPath) {
            return res.status(400).json({
                success: false,
                error: '缺少项目路径'
            });
        }

        if (!fs.existsSync(extractPath)) {
            return res.status(404).json({
                success: false,
                error: '项目路径不存在'
            });
        }

        const analyzerScript = '/root/preprocess/Server.py'; 
        let command = `python3 "${analyzerScript}" analyze_repo "${extractPath}"`;

        if (clearDb) {
            command += ' --clear-db';
        }

        console.log('执行仓库分析:', command);

        try {
            // 仓库分析可能需要更长时间
            const { stdout, stderr } = await execPromise(command, {
                maxBuffer: 100 * 1024 * 1024,
                timeout: 300000, // 5分钟超时
                encoding: 'utf8'
            });

            console.log('原始 stdout:', stdout);
            console.log('原始 stderr:', stderr);

            // 尝试找到 JSON 输出
            let analysisResult;
            
            // 方法1: 尝试解析整个 stdout
            try {
                analysisResult = JSON.parse(stdout);
            } catch (parseError) {
                // 方法2: 尝试从输出中提取 JSON
                const jsonMatch = stdout.match(/\{[\s\S]*\}(?!.*\{)/);
                if (jsonMatch) {
                    try {
                        analysisResult = JSON.parse(jsonMatch[0]);
                    } catch (e) {
                        // 方法3: 查找以 { 开始 } 结束的内容
                        const lines = stdout.split('\n');
                        let jsonStr = '';
                        let inJson = false;
                        let braceCount = 0;
                        
                        for (const line of lines) {
                            if (line.trim().startsWith('{')) {
                                inJson = true;
                                jsonStr = '';
                            }
                            
                            if (inJson) {
                                jsonStr += line + '\n';
                                braceCount += (line.match(/\{/g) || []).length;
                                braceCount -= (line.match(/\}/g) || []).length;
                                
                                if (braceCount === 0 && jsonStr.trim()) {
                                    try {
                                        analysisResult = JSON.parse(jsonStr);
                                        break;
                                    } catch (e) {
                                        // 继续尝试
                                    }
                                }
                            }
                        }
                    }
                }
                
                if (!analysisResult) {
                    // 如果还是无法解析，返回一个默认结构
                    analysisResult = {
                        status: 'completed',
                        message: '分析完成，但输出格式不正确',
                        details: stdout.substring(0, 500) // 返回部分输出用于调试
                    };
                }
            }

            res.json({
                success: true,
                data: analysisResult
            });

        } catch (execError) {
            console.error('执行错误:', execError);
            
            // 检查是否是超时错误
            if (execError.killed || execError.signal === 'SIGTERM') {
                res.status(500).json({
                    success: false,
                    error: '仓库分析超时',
                    details: '分析时间超过5分钟'
                });
            } else {
                res.status(500).json({
                    success: false,
                    error: '仓库分析失败',
                    details: execError.message
                });
            }
        }

    } catch (error) {
        console.error('仓库分析失败:', error);
        res.status(500).json({
            success: false,
            error: '仓库分析失败',
            details: error.message
        });
    }
});



// 批量分析多个文件
app.post('/api/analyze-batch', async (req, res) => {
    try {
        const { files, extractPath, analysisTypes = ['all'] } = req.body;

        if (!files || !Array.isArray(files) || files.length === 0) {
            return res.status(400).json({
                success: false,
                error: '没有提供文件列表'
            });
        }

        const results = [];
        const analyzerScript = path.join(__dirname, 'analyzers', 'integrated_analyzer.py');

        for (const filePath of files) {
            try {
                const fullFilePath = path.join(extractPath, filePath);

                if (!fs.existsSync(fullFilePath)) {
                    results.push({
                        file: filePath,
                        success: false,
                        error: '文件不存在'
                    });
                    continue;
                }

                const command = `python3 "${analyzerScript}" analyze_file "${fullFilePath}" --source-dir "${extractPath}"`;

                const { stdout, stderr } = await execPromise(command, {
                    maxBuffer: 20 * 1024 * 1024,
                    timeout: 60000
                });

                const analysisResult = JSON.parse(stdout);

                results.push({
                    file: filePath,
                    success: true,
                    data: analysisResult
                });

            } catch (error) {
                results.push({
                    file: filePath,
                    success: false,
                    error: error.message
                });
            }
        }

        res.json({
            success: true,
            results: results,
            summary: {
                total: files.length,
                successful: results.filter(r => r.success).length,
                failed: results.filter(r => !r.success).length
            }
        });

    } catch (error) {
        console.error('批量分析失败:', error);
        res.status(500).json({
            success: false,
            error: '批量分析失败',
            details: error.message
        });
    }
});
// 辅助函数：递归获取文件树
async function getFileTree(dir, baseDir = dir) {
    const items = [];
    const files = fs.readdirSync(dir);

    for (const file of files) {
        const fullPath = path.join(dir, file);
        const relativePath = path.relative(baseDir, fullPath);
        const stat = fs.statSync(fullPath);

        if (stat.isDirectory()) {
            const children = await getFileTree(fullPath, baseDir);
            items.push({
                id: relativePath,
                name: file,
                type: 'folder',
                path: relativePath,
                fullPath: fullPath,
                children: children
            });
        } else {
            items.push({
                id: relativePath,
                name: file,
                type: 'file',
                path: relativePath,
                fullPath: fullPath,
                size: stat.size
            });
        }
    }

    return items;
}

// 计算文件数量
function countFiles(items) {
    let count = 0;
    for (const item of items) {
        if (item.type === 'file') {
            count++;
        } else if (item.children) {
            count += countFiles(item.children);
        }
    }
    return count;
}

app.listen(PORT, () => {
    console.log(`服务器运行在 http://localhost:${PORT}`);
});
