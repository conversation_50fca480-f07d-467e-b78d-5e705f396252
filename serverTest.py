from Server import IntegratedAnalyzer

# 初始化分析器（带源目录）
analyzer = IntegratedAnalyzer(source_directory="/root/preprocess/testrepos")

# # 分析单个文件
# results = analyzer.analyze_file("/path/to/your/c/project/main.c", ['all'])

# 仅进行 SAST 分析
sast_results = analyzer.analyze_file("/root/preprocess/testrepos/1.c", ['sast'])

# # 仅进行 CWE 分析
# cwe_results = analyzer.analyze_file("/path/to/your/c/project/main.c", ['cwe'])

# # 分析整个仓库
# repo_results = analyzer.analyze_repository("/path/to/your/c/project", clear_db=True)

# 打印结果
import json
# print(json.dumps(results, indent=2))

# 清理资源
analyzer.cleanup()
