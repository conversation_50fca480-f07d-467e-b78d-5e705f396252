#!/usr/bin/env python3
"""
Integrated Code Analyzer
Combines SAST analysis, CWE similarity search, and Neo4j dependency analysis
"""

from datetime import datetime  

import os
import sys
import json
import logging
import argparse
import subprocess
from typing import Dict, List, Any, Optional
from pathlib import Path

# Add necessary paths to sys.path
sys.path.append('/root/preprocess')
sys.path.append('/root/preprocess/neo4j')
sys.path.append('/root/preprocess/SAST')
sys.path.append('/root/preprocess/Main')
sys.path.append('/root/preprocess/Treesitter')

# Import required modules
from SAST import copy_c_h_files_to_repo, run_pvs_studio_analysis, parse_pvs_analysis_report
from utils import preprocess_code, perform_query
from myneo4j import CodeRepositoryGraphGenerator
from TreeSitterParser import TreeSitterParser

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class IntegratedAnalyzer:
    """
    Integrated analyzer that combines multiple analysis techniques:
    - SAST (Static Application Security Testing)
    - CWE similarity search
    - Neo4j dependency analysis
    """
    
    def __init__(self, config: Dict[str, Any] = None, source_directory: str = None):
        """
        Initialize the integrated analyzer
        
        Args:
            config: Configuration dictionary with paths and credentials
            source_directory: Directory containing source code to analyze
        """
        self.config = config or self._get_default_config()
        self.source_directory = source_directory
        self._initialize_components()
    def generate_llm_prompt(self, file_path: str) -> Dict[str, Any]:
        """
        为指定文件生成LLM分析prompt并发送给LLM
        
        Args:
            file_path: 文件路径
        
        Returns:
            包含prompt信息和LLM响应的字典
        """
        # 先进行全面分析
        analysis_results = self.analyze_file(file_path, ['all'])
        
        # 读取源代码
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                source_code = f.read()
        except:
            source_code = None
        
        # 生成prompt
        prompt = convert_to_prompt(analysis_results, source_code)
        
        # 保存prompt到文件
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        base_name = Path(file_path).stem
        
        # 创建prompts目录
        prompt_dir = "/root/preprocess/testrepos/prompts"
        os.makedirs(prompt_dir, exist_ok=True)
        
        prompt_filename = f"{base_name}_{timestamp}_prompt.txt"
        prompt_path = os.path.join(prompt_dir, prompt_filename)
        
        with open(prompt_path, 'w', encoding='utf-8') as f:
            f.write(prompt)
        
        # 发送prompt给LLM并获取响应
        llm_result = self.send_prompt_to_llm(prompt, file_path)
        
        # 返回综合结果
        return {
            'success': True,
            'prompt_path': prompt_path,
            'file_analyzed': file_path,
            'prompt_length': len(prompt),
            'timestamp': timestamp,
            'llm_response': llm_result.get('llm_response'),
            'llm_response_path': llm_result.get('response_path'),
            'detected_vulnerabilities': llm_result.get('vulnerabilities', []),
            'detected_cwes': llm_result.get('detected_cwes', []),
            'vulnerability_count': llm_result.get('vulnerability_count', 0),
            'llm_error': llm_result.get('error')
        }

    def send_prompt_to_llm(self, prompt_content: str, file_path: str) -> Dict[str, Any]:
        """
        将prompt发送给LLM模型并保存返回结果
        
        Args:
            prompt_content: 要发送的prompt内容
            file_path: 原始分析的文件路径
        
        Returns:
            包含LLM响应信息的字典
        """
        from openai import OpenAI
        import json
        
        result = {
            'success': False,
            'error': None,
            'llm_response': None,
            'response_path': None,
            'file_analyzed': file_path
        }
        
        try:
            # 创建OpenAI客户端
            client = OpenAI(
                api_key="sk-HPA4ad4f5f96d49522c0f3011ccde5acc10ac3e2b7bLHhMS",  # 您在 2233 创建的 key
                base_url="https://api.gptsapi.net/v1"  # 提供的 url
            )
            
            logger.info("Sending prompt to LLM...")
            
            # 发送请求
            response = client.chat.completions.create(
                model="claude-3-5-sonnet-20241022",
                messages=[
                    {
                        "role": "system",
                        "content": "你是一个专业的漏洞检测和安全分析专家。请根据提供的代码分析结果，准确识别真实存在的漏洞。"
                    },
                    {
                        "role": "user",
                        "content": prompt_content
                    }
                ],
                temperature=0.3,  # 降低温度以获得更确定的结果
                max_tokens=9000
            )
            
            # 获取响应内容
            llm_response = response.choices[0].message.content
            result['llm_response'] = llm_response
            
            # 解析LLM响应中的JSON部分
            try:
                # 尝试从响应中提取JSON
                import re
                json_pattern = r'\{[^{}]*"源代码中确认存在漏洞的CWE_ID"[^{}]*\}'
                json_matches = re.findall(json_pattern, llm_response, re.DOTALL)
                
                vulnerabilities = []
                for match in json_matches:
                    try:
                        vuln = json.loads(match)
                        vulnerabilities.append(vuln)
                    except:
                        pass
                
                result['vulnerabilities'] = vulnerabilities
                result['vulnerability_count'] = len(vulnerabilities)
                
                # 提取所有CWE ID
                all_cwes = []
                for vuln in vulnerabilities:
                    cwes = vuln.get('源代码中确认存在漏洞的CWE_ID', [])
                    all_cwes.extend(cwes)
                result['detected_cwes'] = list(set(all_cwes))
                
            except Exception as e:
                logger.warning(f"Failed to parse JSON from LLM response: {e}")
            
            # 保存LLM响应到文件
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            base_name = Path(file_path).stem
            
            # 创建responses目录
            response_dir = "/root/preprocess/testrepos/llm_responses"
            os.makedirs(response_dir, exist_ok=True)
            
            response_filename = f"{base_name}_{timestamp}_llm_response.json"
            response_path = os.path.join(response_dir, response_filename)
            
            # 保存完整的响应数据
            response_data = {
                'file_path': file_path,
                'timestamp': timestamp,
                'prompt_length': len(prompt_content),
                'llm_model': 'claude-3-5-sonnet-20241022',
                'raw_response': llm_response,
                'parsed_vulnerabilities': result.get('vulnerabilities', []),
                'detected_cwes': result.get('detected_cwes', []),
                'vulnerability_count': result.get('vulnerability_count', 0)
            }
            
            with open(response_path, 'w', encoding='utf-8') as f:
                json.dump(response_data, f, indent=2, ensure_ascii=False)
            
            result['response_path'] = response_path
            result['success'] = True
            
            logger.info(f"LLM response saved to: {response_path}")
            
        except Exception as e:
            logger.error(f"Failed to get LLM response: {e}")
            result['error'] = str(e)
        
        return result

    def initialize_sast_files(self):
        """Public method to initialize SAST files"""
        try:
            results = {
            'repo_path': self.source_directory,
            'success': False,
            'error': None
        }
            # Process repository with Neo4j
            if self.graph_generator:
                success = self.graph_generator.process_repository(self.source_directory, clear_db)
                results['success'] = success
                
                if not success:
                    results['error'] = "Failed to process repository"
            else:
                results['error'] = "Neo4j analyzer not initialized"
                
        except Exception as e:
            logger.error(f"Repository analysis failed: {e}")
            results['error'] = str(e)
        return self._initialize_sast_files()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """Get default configuration"""
        return {
            "sast": {
                "output_dir": "/root/preprocess/SAST",
                "project_dir": "/root/preprocess/SAST",
                "repo_dir": "/root/preprocess/SAST/repo"
            },
            "neo4j": {
                "uri": "bolt://localhost:7687",
                "username": "neo4j",
                "password": "password",
                "import_path": "/var/lib/neo4j/import"
            },
            "tree_sitter": {
                "language_path": "/opt/tree-sitter/languages/tree-sitter-c/build/my-languages.so"
            },
            "cwe": {
                "index_path": "/root/preprocess/large_embedding_index.faiss",
                "top_k": 5
            }
        }
    
    def _initialize_components(self):
        """Initialize analysis components"""
        try:
            # Initialize Neo4j graph generator
            self.graph_generator = CodeRepositoryGraphGenerator(
                neo4j_uri=self.config["neo4j"]["uri"],
                neo4j_user=self.config["neo4j"]["username"],
                neo4j_password=self.config["neo4j"]["password"],
                neo4j_import_path=self.config["neo4j"]["import_path"],
                tree_sitter_language_path=self.config["tree_sitter"]["language_path"]
            )
            logger.info("Neo4j analyzer initialized")
        except Exception as e:
            logger.error(f"Failed to initialize Neo4j analyzer: {e}")
            self.graph_generator = None
    
    def _initialize_sast_files(self):
        """Initialize SAST by copying source files to repo directory"""
        try:
            logger.info(f"Initializing SAST files from {self.source_directory}")
            
            # Copy C/C++ files to SAST repo directory
            copied_files = copy_c_h_files_to_repo(
                self.source_directory, 
                self.config["sast"]["repo_dir"]
            )
            
            self.sast_files_copied = copied_files
            logger.info(f"Copied {len(copied_files)} files to SAST repo directory")
            
        except Exception as e:
            logger.error(f"Failed to initialize SAST files: {e}")
            self.sast_files_copied = []
    
    def analyze_file(self, file_path: str, analysis_types: List[str] = None) -> Dict[str, Any]:
        """
        Perform comprehensive analysis on a file
        
        Args:
            file_path: Path to the file to analyze
            analysis_types: List of analysis types to perform
                          ['sast', 'cwe', 'dependencies', 'all']
        
        Returns:
            Dictionary containing analysis results
        """
        if analysis_types is None:
            analysis_types = ['all']
        
        results = {
            'file_path': file_path,
            'analysis_types': analysis_types,
            'sast': {},
            'cwe': {},
            'dependencies': {},
            'summary': {}
        }
        
        # Read file content
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                code_content = f.read()
        except Exception as e:
            logger.error(f"Failed to read file {file_path}: {e}")
            results['error'] = f"Failed to read file: {str(e)}"
            return results
        
        # Perform SAST analysis
        if 'sast' in analysis_types or 'all' in analysis_types:
            logger.info("Performing SAST analysis...")
            results['sast'] = self._perform_sast_analysis(code_content, file_path)
        
        # Perform CWE similarity search
        if 'cwe' in analysis_types or 'all' in analysis_types:
            logger.info("Performing CWE similarity search...")
            results['cwe'] = self._perform_cwe_search(code_content)
        
        # Perform dependency analysis
        if 'dependencies' in analysis_types or 'all' in analysis_types:
            logger.info("Performing dependency analysis...")
            results['dependencies'] = self._perform_dependency_analysis(file_path)
        
        # Generate summary
        results['summary'] = self._generate_summary(results)
        
        return results
    
    def _perform_sast_analysis(self, code_content: str, file_path: str) -> Dict[str, Any]:
        """Perform SAST analysis using PVS-Studio"""
        sast_results = {
            'vulnerabilities': [],
            'error': None
        }
        
        try:
            # Check if we need to copy files first
            # if not hasattr(self, 'sast_files_copied') or not self.sast_files_copied:
            #     # Get the directory of the file
            #     source_dir = os.path.dirname(file_path)
            #     if not source_dir:
            #         source_dir = os.getcwd()
                
            #     # Copy files to repo directory
            #     logger.info(f"Copying files from {source_dir} to SAST repo")
            #     copied_files = copy_c_h_files_to_repo(
            #         source_dir, 
            #         self.config["sast"]["repo_dir"]
            #     )
            #     self.sast_files_copied = copied_files
            
            # Run PVS-Studio analysis
            logger.info("Running PVS-Studio analysis...")
            report_path = run_pvs_studio_analysis(self.config["sast"]["project_dir"])
            
            if report_path and os.path.exists(report_path):
                # Parse the report
                report_content = parse_pvs_analysis_report(report_path)
                
                # Parse the string report into structured data
                vulnerabilities = self._parse_sast_report(report_content)
                sast_results['vulnerabilities'] = vulnerabilities
                sast_results['total_issues'] = len(vulnerabilities)
                
                # Filter vulnerabilities for the specific file if needed
                file_name = os.path.basename(file_path)
                file_specific_vulns = [
                    v for v in vulnerabilities 
                    if file_name in v.get('file', '')
                ]
                
                if file_specific_vulns:
                    sast_results['file_specific_vulnerabilities'] = file_specific_vulns
                
                # Categorize by severity
                severity_counts = {}
                for vuln in vulnerabilities:
                    severity = vuln.get('error_type', 'Unknown')
                    severity_counts[severity] = severity_counts.get(severity, 0) + 1
                
                sast_results['severity_distribution'] = severity_counts
            else:
                sast_results['error'] = "Failed to generate SAST report"
                
        except Exception as e:
            logger.error(f"SAST analysis failed: {e}")
            sast_results['error'] = str(e)
        
        return sast_results
    
    def _parse_sast_report(self, report_content: str) -> List[Dict[str, Any]]:
        """Parse SAST report string into structured data"""
        vulnerabilities = []
        
        if not report_content:
            return vulnerabilities
        
        # Split by delimiter
        entries = report_content.split('-' * 50)
        
        for entry in entries:
            if not entry.strip():
                continue
            
            vuln = {}
            lines = entry.strip().split('\n')
            
            for line in lines:
                if line.startswith('Error Type:'):
                    vuln['error_type'] = line.split(':', 1)[1].strip()
                elif line.startswith('Error Code:'):
                    vuln['error_code'] = line.split(':', 1)[1].strip()
                elif line.startswith('Message:'):
                    vuln['message'] = line.split(':', 1)[1].strip()
                elif line.startswith('File:'):
                    vuln['file'] = line.split(':', 1)[1].strip()
                elif line.startswith('Line:'):
                    vuln['line'] = line.split(':', 1)[1].strip()
                elif line.startswith('CWE Code:'):
                    vuln['cwe_code'] = line.split(':', 1)[1].strip()
            
            if vuln:
                vulnerabilities.append(vuln)
        
        return vulnerabilities
    
    def _perform_cwe_search(self, code_content: str) -> Dict[str, Any]:
        """Perform CWE similarity search"""
        cwe_results = {
            'similar_cwes': [],
            'error': None
        }
        
        try:
            # Preprocess the code
            preprocessed_code = preprocess_code(code_content)
            
            # Perform query
            perform_query(preprocessed_code)
            
            # Read the results from the saved JSON file
            results_file = '/root/preprocess/top_results.json'
            if os.path.exists(results_file):
                with open(results_file, 'r', encoding='utf-8') as f:
                    similar_cwes = json.load(f)
                
                cwe_results['similar_cwes'] = similar_cwes
                
                # Extract unique CWE IDs
                unique_cwes = list(set(item['CWE_ID'] for item in similar_cwes if 'CWE_ID' in item))
                cwe_results['unique_cwe_ids'] = unique_cwes
                
                # Count vulnerabilities
                vuln_count = sum(1 for item in similar_cwes if item.get('IF_VUL', False))
                cwe_results['vulnerability_count'] = vuln_count
                
        except Exception as e:
            logger.error(f"CWE search failed: {e}")
            cwe_results['error'] = str(e)
        
        return cwe_results
    
    def _perform_dependency_analysis(self, file_path: str) -> Dict[str, Any]:
        """Perform dependency analysis using Neo4j"""
        dependency_results = {
            'external_dependencies': {},
            'definitions_found': {},
            'file_dependencies': {},
            'error': None
        }
        
        try:
            if self.graph_generator:
                # Analyze file dependencies
                analysis = self.graph_generator.analyze_file_dependencies(file_path)
                
                dependency_results['external_dependencies'] = analysis.get('external_dependencies', {})
                dependency_results['definitions_found'] = analysis.get('definitions_found', {})
                dependency_results['file_dependencies'] = analysis.get('file_dependencies', {})
                dependency_results['summary'] = analysis.get('summary', {})
            else:
                dependency_results['error'] = "Neo4j analyzer not initialized"
                
        except Exception as e:
            logger.error(f"Dependency analysis failed: {e}")
            dependency_results['error'] = str(e)
        
        return dependency_results
    
    def _generate_summary(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """Generate a comprehensive summary of all analyses"""
        summary = {
            'total_issues': 0,
            'risk_level': 'Low',
            'key_findings': [],
            'recommendations': []
        }
        
        # SAST summary
        if 'sast' in results and not results['sast'].get('error'):
            sast_issues = len(results['sast'].get('vulnerabilities', []))
            summary['total_issues'] += sast_issues
            
            if sast_issues > 0:
                summary['key_findings'].append(f"Found {sast_issues} potential vulnerabilities via SAST")
                
                # Add CWE codes from SAST
                sast_cwes = [v.get('cwe_code', 'N/A') for v in results['sast'].get('vulnerabilities', [])]
                sast_cwes = [cwe for cwe in sast_cwes if cwe != 'N/A']
                if sast_cwes:
                    summary['key_findings'].append(f"SAST identified CWEs: {', '.join(set(sast_cwes))}")
        
        # CWE similarity summary
        if 'cwe' in results and not results['cwe'].get('error'):
            similar_cwes = results['cwe'].get('similar_cwes', [])
            if similar_cwes:
                top_cwe = similar_cwes[0] if similar_cwes else None
                if top_cwe:
                    summary['key_findings'].append(
                        f"Code shows similarity to CWE-{top_cwe.get('CWE_ID', 'Unknown')} "
                        f"(similarity distance: {top_cwe.get('distance', 'N/A'):.2f})"
                    )
                
                vuln_count = results['cwe'].get('vulnerability_count', 0)
                if vuln_count > 0:
                    summary['key_findings'].append(
                        f"{vuln_count} of {len(similar_cwes)} similar code patterns are known vulnerabilities"
                    )
        
        # Dependency summary
        if 'dependencies' in results and not results['dependencies'].get('error'):
            dep_summary = results['dependencies'].get('summary', {})
            
            total_external = sum([
                dep_summary.get('total_external_functions', 0),
                dep_summary.get('total_external_variables', 0),
                dep_summary.get('total_external_types', 0),
                dep_summary.get('total_external_macros', 0)
            ])
            
            if total_external > 0:
                summary['key_findings'].append(f"Found {total_external} external dependencies")
                
                # Check for undefined dependencies
                undefined = total_external - sum([
                    dep_summary.get('functions_with_definitions', 0),
                    dep_summary.get('variables_with_definitions', 0),
                    dep_summary.get('types_with_definitions', 0),
                    dep_summary.get('macros_with_definitions', 0)
                ])
                
                if undefined > 0:
                    summary['key_findings'].append(f"{undefined} dependencies have no definitions found")
                    summary['recommendations'].append("Review and ensure all external dependencies are properly defined")
        
        # Determine risk level
        if summary['total_issues'] == 0:
            summary['risk_level'] = 'Low'
        elif summary['total_issues'] <= 5:
            summary['risk_level'] = 'Medium'
        else:
            summary['risk_level'] = 'High'
        
        # Add general recommendations
        if summary['total_issues'] > 0:
            summary['recommendations'].append("Conduct a thorough code review focusing on identified issues")
            summary['recommendations'].append("Implement secure coding practices")
        
        return summary
    
    def analyze_repository(self, repo_path: str, clear_db: bool = True) -> Dict[str, Any]:
        """
        Analyze an entire repository
        
        Args:
            repo_path: Path to the repository
            clear_db: Whether to clear the Neo4j database before analysis
        
        Returns:
            Analysis results
        """
        results = {
            'repo_path': repo_path,
            'success': False,
            'error': None
        }
        
        try:
            # Copy repository files for SAST analysis
            logger.info(f"Copying repository files for SAST analysis from {repo_path}")
            copied_files = copy_c_h_files_to_repo(
                repo_path, 
                self.config["sast"]["repo_dir"]
            )
            self.sast_files_copied = copied_files
            
            # Process repository with Neo4j
            if self.graph_generator:
                success = self.graph_generator.process_repository(repo_path, clear_db)
                results['success'] = success
                
                if not success:
                    results['error'] = "Failed to process repository"
            else:
                results['error'] = "Neo4j analyzer not initialized"
                
        except Exception as e:
            logger.error(f"Repository analysis failed: {e}")
            results['error'] = str(e)
        
        return results
    
    def cleanup(self):
        """Clean up resources"""
        if self.graph_generator:
            self.graph_generator.close()
def convert_to_prompt(analysis_results: Dict[str, Any], source_code: str = None) -> str:
    """
    将分析结果转换为LLM prompt格式
    """
   
    
    prompt_parts = []
    
    # 任务描述
    prompt_parts.append("""任务: 你是一个漏洞检测和安全分析专家，负责评估和过滤潜在的漏洞风险。你的任务是根据以下内容分析代码：
--你的回复最后必须是如下的json！！！！：
   {
      "源代码中确认存在漏洞的CWE_ID": ["CWE-xxx"]
      "推理过程": "详细描述确认的漏洞。"
      "在文件中的行数":"xx行"
   },
   {
      "源代码中确认存在漏洞的CWE_ID": ["CWE-xxx"]
      "推理过程": "详细描述确认的漏洞。"
      "在文件中的行数":"xx行"
   }
""")
    
    # 1. 源代码
    if source_code:
        prompt_parts.append(f"1. 源代码:\n```c\n{source_code}\n```")
    else:
        # 尝试从文件路径读取
        file_path = analysis_results.get('file_path')
        if file_path and os.path.exists(file_path):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    code = f.read()
                prompt_parts.append(f"1. 源代码:\n```c\n{code}\n```")
            except:
                prompt_parts.append("1. 源代码: [无法读取源代码]")
        else:
            prompt_parts.append("1. 源代码: [未提供]")
    
    # 2. 逐步推理过程
    prompt_parts.append("""
2. 逐步推理（COT）过程:
    - 请对提供的源代码进行分析，识别潜在的漏洞风险。""")
    
    # 3. SAST分析结果
    prompt_parts.append("\n3. 从静态分析工具（SAST）获得的漏洞警告:")
    sast_results = analysis_results.get('sast', {})
    
    if sast_results.get('error'):
        prompt_parts.append(f"   SAST分析出错: {sast_results['error']}")
    elif sast_results.get('vulnerabilities'):
        prompt_parts.append("   我们通过 SAST 工具进行了初步的静态代码分析，其中每个漏洞包含漏洞位置、漏洞类型和详细描述。")
        
        for vuln in sast_results['vulnerabilities']:
            prompt_parts.append(f"""
   - 错误类型: {vuln.get('error_type', 'Unknown')}
     错误代码: {vuln.get('error_code', 'N/A')}
     CWE代码: {vuln.get('cwe_code', 'N/A')}
     文件: {vuln.get('file', 'Unknown')}
     行号: {vuln.get('line', 'N/A')}
     消息: {vuln.get('message', 'No message')}""")
    else:
        prompt_parts.append("   SAST分析未发现漏洞警告。")
    
    # 4. 依赖分析结果 - 修复这部分
    prompt_parts.append("\n4. 从外部依赖解析得到的代码外部上下文:")
    dep_results = analysis_results.get('dependencies', {})
    
    if dep_results.get('error'):
        prompt_parts.append(f"   依赖分析出错: {dep_results['error']}")
    else:
        # 外部依赖
        ext_deps = dep_results.get('external_dependencies', {})
        defs_found = dep_results.get('definitions_found', {})
        
        # 处理外部函数及其定义
        ext_funcs = ext_deps.get('functions', [])
        if ext_funcs:
            prompt_parts.append("\n   外部函数调用:")
            for func_info in ext_funcs[:10]:  # 限制数量
                if isinstance(func_info, dict):
                    func_name = func_info.get('name', 'Unknown')
                    line = func_info.get('line', 'N/A')
                    context = func_info.get('context', '')
                    prompt_parts.append(f"   - 函数名: {func_name} (行 {line})")
                    prompt_parts.append(f"     调用上下文: {context}")
                    
                    # 查找对应的定义
                    func_defs = defs_found.get('functions', {}).get(func_name, [])
                    if func_defs:
                        for def_info in func_defs:
                            prompt_parts.append(f"     定义位置: {def_info.get('file_path', 'Unknown')} (行 {def_info.get('line_number', 'N/A')})")
                            prompt_parts.append(f"     函数签名: {def_info.get('signature', 'N/A')}")
                            # 添加函数定义代码（限制长度）
                            def_code = def_info.get('definition_code', '')
                            if def_code:
                                # 只显示前几行
                                lines = def_code.split('\n')
                                prompt_parts.append("     定义代码:")
                                for line in lines:
                                    prompt_parts.append(f"       {line}")
                                if len(def_code.split('\n')) > 5:
                                    prompt_parts.append("       ...")
                    else:
                        prompt_parts.append("     定义: 未找到")
                else:
                    prompt_parts.append(f"   - {func_info}")
        
        # 处理外部变量
        ext_vars = ext_deps.get('variables', [])
        if ext_vars:
            prompt_parts.append("\n   外部变量引用:")
            for var_info in ext_vars[:10]:
                if isinstance(var_info, dict):
                    var_name = var_info.get('name', 'Unknown')
                    line = var_info.get('line', 'N/A')
                    context = var_info.get('context', '')
                    prompt_parts.append(f"   - 变量名: {var_name} (行 {line})")
                    prompt_parts.append(f"     使用上下文: {context}")
                    
                    # 查找变量定义
                    var_defs = defs_found.get('variables', {}).get(var_name, [])
                    if var_defs:
                        for def_info in var_defs:
                            if isinstance(def_info, dict):
                                prompt_parts.append(f"     类型: {def_info.get('type_full_name', 'Unknown')}")
                else:
                    prompt_parts.append(f"   - {var_info}")
        
        # 处理宏
        ext_macros = ext_deps.get('macros', [])
        if ext_macros:
            prompt_parts.append("\n   外部宏:")
            for macro_info in ext_macros[:10]:
                if isinstance(macro_info, dict):
                    macro_name = macro_info.get('name', 'Unknown')
                    prompt_parts.append(f"   - {macro_name}")
                else:
                    prompt_parts.append(f"   - {macro_info}")
        
        # 处理类型
        ext_types = ext_deps.get('types', [])
        if ext_types:
            prompt_parts.append("\n   外部类型:")
            for type_info in ext_types[:10]:
                if isinstance(type_info, dict):
                    type_name = type_info.get('name', 'Unknown')
                    prompt_parts.append(f"   - {type_name}")
                else:
                    prompt_parts.append(f"   - {type_info}")
        
        # 如果没有外部依赖
        if not (ext_funcs or ext_vars or ext_macros or ext_types):
            prompt_parts.append("   未发现外部依赖。")
    
    # 5. CWE相似性搜索结果
    prompt_parts.append("""\n5. 通过 RAG（基于相似代码的检索和语义分析）方法获得:
- - 请注意，RAG 方法可能会产生的结果只是单纯的与代码相关的CWE,而非对该代码的实际检测结果，请不用把它当作漏洞，而是单纯的参考。""")
    
    cwe_results = analysis_results.get('cwe', {})
    if cwe_results.get('error'):
        prompt_parts.append(f"   CWE搜索出错: {cwe_results['error']}")
    elif cwe_results.get('similar_cwes'):
        prompt_parts.append("   我们通过 RAG 方法从漏洞库中检索并分析了相似的CWE类型：")
        
        for result in cwe_results['similar_cwes'][:3]:  # 只取前3个
            distance = result.get('distance', 'N/A')
            if isinstance(distance, (int, float)):
                distance_str = f"{distance:.4f}"
            else:
                distance_str = str(distance)
                
            prompt_parts.append(f"""   - CWE_ID: {result.get('CWE_ID', 'Unknown')}
            距离: {distance_str}""")
        
        prompt_parts.append("- - 请注意，详细考虑与RAG方法返回CWE相关的其他CWE，如CWE-416与CWE-415具有相似性，都应考虑。")
    else:
        prompt_parts.append("   未找到相似的CWE模式。")
    
    # 最后的要求
    prompt_parts.append("""
要求:
- 请严格过滤出现的误报漏洞，对于较为罕见的情况（例如malloc分配失败导致指针为空）请不用考虑
- 请独立自主的进行思考，不完全依赖RAG与SAST 得到你可以确认的CWE 以及相应推理过程
- 代码中可能含有多个cwe 请注意！

--你的回复最后必须是如下的json！！！！：
    {
        "源代码中确认存在漏洞的CWE_ID": ["CWE-xxx"],
        "推理过程": "详细描述确认的漏洞。"
        "在文件中的行数":"xx行"
    },
   {
      "源代码中确认存在漏洞的CWE_ID": ["CWE-xxx"]
      "推理过程": "详细描述确认的漏洞。"
      "在文件中的行数":"xx行"
   }

""")
    
    return "\n".join(prompt_parts)

def debug_analysis_results(results_file: str):
    """
    调试函数，打印分析结果的结构
    
    Args:
        results_file: 分析结果JSON文件路径
    """
    import json
    
    try:
        with open(results_file, 'r', encoding='utf-8') as f:
            results = json.load(f)
        
        print("=== 分析结果结构 ===")
        print(f"顶层键: {list(results.keys())}")
        
        # 检查dependencies结构
        if 'dependencies' in results:
            deps = results['dependencies']
            print(f"\ndependencies键: {list(deps.keys())}")
            
            if 'external_dependencies' in deps:
                ext_deps = deps['external_dependencies']
                print(f"external_dependencies类型: {type(ext_deps)}")
                if isinstance(ext_deps, dict):
                    print(f"external_dependencies键: {list(ext_deps.keys())}")
                    
                    # 检查每个子项的类型
                    for key in ['functions', 'macros', 'types', 'variables']:
                        if key in ext_deps:
                            item = ext_deps[key]
                            print(f"{key}类型: {type(item)}")
                            if isinstance(item, dict):
                                print(f"{key}数量: {len(item)}")
                                print(f"{key}示例: {list(item.keys())[:3]}")
                            elif isinstance(item, list):
                                print(f"{key}数量: {len(item)}")
                                print(f"{key}示例: {item[:3]}")
        
    except Exception as e:
        print(f"调试失败: {e}")
# 使用示例
def analyze_and_generate_prompt(file_path: str, source_dir: str = None) -> str:
    """
    分析文件并生成prompt
    
    Args:
        file_path: 要分析的文件路径
        source_dir: SAST源目录
    
    Returns:
        生成的prompt字符串
    """
    # 运行分析
    import subprocess
    import json
    
    cmd = ["python", "Server.py", "analyze_file", file_path]
    if source_dir:
        cmd.extend(["--source-dir", source_dir])
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, check=True)
        analysis_results = json.loads(result.stdout)
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                source_code = f.read()
        except:
            source_code = None
        # 生成prompt
        prompt = convert_to_prompt(analysis_results,source_code)
        print(prompt)
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        base_name = Path(file_path).stem
        prompt_filename = f"{base_name}_{timestamp}_prompt.txt"
        prompt_path = os.path.join(f"/root/preprocess/testrepos/prompts", prompt_filename)
        
        with open(prompt_path, 'w', encoding='utf-8') as f:
            f.write(prompt)
        
        result['success'] = True
        result['prompt_path'] = prompt_path
        return prompt
        
    except subprocess.CalledProcessError as e:
        print(f"分析失败: {e}")
        return None
    except json.JSONDecodeError as e:
        print(f"解析结果失败: {e}")
        return None


# 如果已有分析结果，直接转换
def convert_existing_results(results_file: str) -> str:
    """
    从已有的分析结果文件生成prompt
    
    Args:
        results_file: 分析结果JSON文件路径
    
    Returns:
        生成的prompt字符串
    """
    try:
        with open(results_file, 'r', encoding='utf-8') as f:
            analysis_results = json.load(f)
        
        return convert_to_prompt(analysis_results)
    except Exception as e:
        print(f"读取结果文件失败: {e}")
        return None


def main():
    """Main function for command-line usage"""
    parser = argparse.ArgumentParser(description="Integrated Code Analyzer")
    parser.add_argument("action", choices=["analyze_file", "analyze_repo", "sast", "cwe", "init", "dependencies","prompt"],
                        help="Action to perform")
    parser.add_argument("path", help="File or repository path")
    parser.add_argument("--output", "-o", help="Output file for results (JSON)")
    parser.add_argument("--clear-db", action="store_true", help="Clear Neo4j database before analysis")
    parser.add_argument("--source-dir", help="Source directory for SAST initialization")
    
    args = parser.parse_args()
    
    # Determine source directory for SAST
    # source_dir = args.source_dir
    # if not source_dir:
    #     if args.action == "analyze_repo":
    #         source_dir = args.path
    #     elif os.path.isfile(args.path):
    #         source_dir = os.path.dirname(args.path)
    
    source_dir = args.source_dir if args.source_dir else args.path

    # Initialize analyzer with source directory
    analyzer = IntegratedAnalyzer(source_directory=source_dir)
    
    try:
        results = {}
        

        if args.action == "init":
            analyzer.initialize_sast_files()
        if args.action == "prompt":
            result = analyzer.generate_llm_prompt(args.path)
            print(json.dumps(result, indent=2, ensure_ascii=False))
            sys.exit(0)

              
        if args.action == "analyze_file":
            # Comprehensive file analysis
            results = analyzer.analyze_file(args.path, ['all'])
        
        elif args.action == "analyze_repo":
            # Repository analysis
            results = analyzer.analyze_repository(args.path, args.clear_db)
        
        elif args.action == "sast":
            # SAST only
            results = analyzer.analyze_file(args.path, ['sast'])
        
        elif args.action == "cwe":
            # CWE search only
            results = analyzer.analyze_file(args.path, ['cwe'])
        
        elif args.action == "dependencies":
            # Dependencies only
            results = analyzer.analyze_file(args.path, ['dependencies'])
        
        # Output results
        if args.output:
            with open(args.output, 'w', encoding='utf-8') as f:
                json.dump(results, f, indent=2, ensure_ascii=False)
        else:
            # Print to stdout for server.js to capture
            print(json.dumps(results, indent=2, ensure_ascii=False))
    
    except Exception as e:
        error_result = {
            'error': str(e),
            'success': False
        }
        print(json.dumps(error_result))
        sys.exit(1)
    
    finally:
        analyzer.cleanup()


if __name__ == "__main__":
    main()