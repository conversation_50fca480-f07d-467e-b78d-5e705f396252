#include <stdio.h>
#include <stdlib.h>
#include <limits.h>
#include "buffer_overflow.h"
#include "memory_leak.h"
#include "integer_overflow.h"
#include "null_pointer.h"
#include "format_string.h"

int main() {
    printf("=== CWE Security Vulnerabilities Demo ===\n\n");
    
    // 测试缓冲区溢出
    printf("1. Testing Buffer Overflow (CWE-120):\n");
    char large_input[] = "This is a very long string that will definitely overflow the buffer in the process_user_input function";
    process_user_input(large_input);
    
    printf("\n2. Testing Memory Leak (CWE-401):\n");
    store_data("Memory leak test");
    
    printf("\n3. Testing Integer Overflow (CWE-190):\n");
    calculate_buffer_size(INT_MAX / 50);
    
    printf("\n4. Testing NULL Pointer Dereference (CWE-476):\n");
    process_string(NULL);
    
    printf("\n5. Testing Format String Vulnerability (CWE-134):\n");
    print_user_message("User input: %x %x %x %x");
    
    return 0;
}
