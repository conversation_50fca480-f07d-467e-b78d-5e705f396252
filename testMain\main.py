from utils import perform_query  # Import the refactored function
from utils import clean_code_block,anonymize_cwe_functions,preprocess_code
from SAST import write_to_main_cpp,run_cmake_and_analyze,parse_pvs_analysis_report
from promptGen import generate_prompt,read_json_file,generate_prompt_2
import json
import re
from pathlib import Path
import requests
import time



def save_response(response: dict, output_dir: Path, file_name: str,prompt: str):
    """结构化保存响应数据"""
    # 原始响应
    raw_response_path = output_dir / f"{file_name}_raw_response.json"
    with raw_response_path.open("w") as f:
        json.dump(response, f)
    
    # 提取核心内容
    try:
        content = response['choices'][0]['message']['content']
        solution_path = output_dir / f"{file_name}_solution2.md"
        with solution_path.open("w") as f:
            f.write(content)

        # 保存到综合 JSON 文件
        combined_results_path = output_dir / "combined_results.json"
        result_entry = {
            "input_file": file_name,
            "prompt": prompt,
            "response": content
        }
        if combined_results_path.exists():
            with combined_results_path.open("r") as f:
                combined_results = json.load(f)
        else:
            combined_results = []

        combined_results.append(result_entry)
        with combined_results_path.open("w") as f:
            json.dump(combined_results, f, indent=4)

    except KeyError:
        print("无效的API响应格式")

def requestLLm(query_code, input_file):
    query_code = preprocess_code(query_code)
    # Perform the query and print the results
    results = perform_query(query_code)
    
    write_to_main_cpp(query_code, output_dir="/root/preprocess/SAST")
    project_dir = "/root/preprocess/SAST/"  

    # 生成报告
    report_path = run_cmake_and_analyze(project_dir)
    time.sleep(3)
    # 输出报告路径
    if report_path:
        print(f"Analysis report generated at: {report_path}")
    else:
        print("Analysis report generation failed.")

    # report_path = "/root/preprocess/SAST/build/pvs_analysis.xml"  

    # 解析并输出分析报告内容
    SAST_Log = parse_pvs_analysis_report(report_path)

    file_path = '/root/preprocess/top_results.json'
    json_data = read_json_file(file_path)

    # 打印读取的JSON内容
    if json_data is not None:
        print("JSON Data Loaded:")
        print(json_data)
    else:
        print("Failed to load JSON data.")

    # prompt = generate_prompt(SAST_Log, json_data, query_code)
    prompt = generate_prompt(SAST_Log, json_data, query_code)
    print(prompt)

    url = "https://api.siliconflow.cn/v1/chat/completions"

    payload = {
        "model": "Qwen/QwQ-32B",
        "stream": False ,
        "max_tokens": 13000 ,
        "temperature": 0.7,
        "top_p": 0.7,
        "top_k": 50,
        "frequency_penalty": 0.5,
        "n": 1,
        "messages": [
            {
                "content": prompt,
                "role": "user"
            }
        ]
    }
    headers = {
        "Authorization": "Bearer sk-eapbrmwvyxhesrlonzxetsqvlhqsrlepmwtkeuzomhktpvcl",
        "Content-Type": "application/json"
    }

    response = requests.request("POST", url, json=payload, headers=headers)
    save_response(response.json(), Path("/root/testsuites/mongoose/results"), input_file, prompt)
    print(response.status_code)
    print(response.text)
def main():
        # 指定目录
    c_files_dir = Path("/root/testsuites/mongoose")  

    # 遍历目录中的每个 .c 文件
    for c_file in c_files_dir.rglob("*.c"):
        with c_file.open("r") as file:
            query_code = file.read()
            # query_code =  repr(query_code)[1:-1]
            requestLLm(query_code, str(c_file))

        
    # with c_files_dir.open("r") as file:
    #     query_code = file.read()
    #     requestLLm(query_code, str(c_files_dir))
    
    
if __name__ == "__main__":
    main()