import os
import json
import shutil
import subprocess
def copy_c_h_files_to_repo(source_dir, output_dir="/root/preprocess/SAST/repo"):
    """
    将源目录中的所有 .c 和 .h 文件复制到目标目录，并添加 PVS-Studio 注释。
    同时生成对应的 compile_commands.json 文件。
    
    Args:
        source_dir (str): 包含 .c 和 .h 文件的源目录
        output_dir (str): PVS-Studio 项目的 repo 目录
    
    Returns:
        list: 复制的文件路径列表
    """
    # 定义需要处理的文件扩展名
    target_extensions = ('.c', '.h', '.cpp', '.hpp', '.cc', '.hh')
    
    # 如果目标目录存在，先删除其中的所有源代码文件
    if os.path.exists(output_dir):
        print(f"清理目标目录 {output_dir} 中的源代码文件...")
        for root, dirs, files in os.walk(output_dir):
            for file in files:
                if file.endswith(target_extensions):
                    file_path = os.path.join(root, file)
                    try:
                        os.remove(file_path)
                        print(f"已删除: {file_path}")
                    except Exception as e:
                        print(f"删除文件失败 {file_path}: {e}")
        
        # 删除空目录
        for root, dirs, files in os.walk(output_dir, topdown=False):
            for dir_name in dirs:
                dir_path = os.path.join(root, dir_name)
                try:
                    if not os.listdir(dir_path):  # 如果目录为空
                        os.rmdir(dir_path)
                        print(f"已删除空目录: {dir_path}")
                except Exception as e:
                    print(f"删除目录失败 {dir_path}: {e}")
    else:
        # 如果目标目录不存在，创建它
        os.makedirs(output_dir)
    
    copied_files = []
    compile_commands = []
    
    # 遍历源目录及其子目录
    for root, dirs, files in os.walk(source_dir):
        for file in files:
            if file.endswith(target_extensions):
                source_path = os.path.join(root, file)
                # 保持相对路径结构
                relative_path = os.path.relpath(source_path, source_dir)
                dest_path = os.path.join(output_dir, relative_path)
                
                # 创建目标目录
                dest_dir = os.path.dirname(dest_path)
                if not os.path.exists(dest_dir):
                    os.makedirs(dest_dir)
                
                # 读取原文件内容
                with open(source_path, 'r', encoding='utf-8', errors='ignore') as f:
                    original_content = f.read()
                
                # 写入带有 PVS-Studio 注释的文件
                with open(dest_path, 'w', encoding='utf-8') as f:
                    # 添加 PVS-Studio 识别的开头注释
                    f.write("// This is an open source non-commercial project. Dear PVS-Studio, please check it.\n")
                    f.write("// PVS-Studio Static Code Analyzer for C, C++ and C#: http://www.viva64.com\n\n")
                    f.write(original_content)
                
                copied_files.append(dest_path)
                
                # 为 .c/.cpp/.cc 文件添加编译命令
                if file.endswith(('.c', '.cpp', '.cc')):
                    compile_command = {
                        "directory": "/root/preprocess/SAST",
                        "command": f"/usr/bin/c++ -o CMakeFiles/pvs-studio-project.dir/{relative_path}.o -c {dest_path}",
                        "file": dest_path
                    }
                    compile_commands.append(compile_command)
    
    # 生成 compile_commands.json
    compile_commands_path = "/root/preprocess/SAST/compile_commands.json"
    with open(compile_commands_path, 'w', encoding='utf-8') as f:
        json.dump(compile_commands, f, indent=2)
    
    print(f"已复制 {len(copied_files)} 个文件到 {output_dir}")
    print(f"已生成 compile_commands.json 文件")
    
    return copied_files

def run_pvs_studio_analysis(project_dir="/root/preprocess/SAST"):
    """
    执行 PVS-Studio 静态分析并生成报告。
    
    Args:
        project_dir (str): PVS-Studio 项目的目录
    
    Returns:
        str: 分析报告的路径
    """
    try:
        # 切换到项目目录
        original_dir = os.getcwd()
        os.chdir(project_dir)
        
        # 执行静态分析
        print("Running PVS-Studio analysis...")
        subprocess.check_call([
            'pvs-studio-analyzer', 
            'analyze', 
            '-f', 'compile_commands.json',
            '-o', 'pvs.log', 
            '-e', 'excludepath',
            '-j', '4'  # 使用4个线程并行分析
        ])

        # 转换分析报告
        print("Converting PVS-Studio log to report...")
        subprocess.check_call([
            'plog-converter', 
            '-a', 'GA:1,2', 
            '-t', 'xml', 
            '-o', 'pvs_analysis.xml', 
            'pvs.log'
        ])
        
        # 返回原目录
        os.chdir(original_dir)
        
        # 获取报告文件的路径
        report_path = os.path.join(project_dir, "pvs_analysis.xml")
        
        if not os.path.exists(report_path):
            raise FileNotFoundError(f"PVS-Studio analysis report not found at {report_path}")

        return report_path

    except subprocess.CalledProcessError as e:
        os.chdir(original_dir)  # 确保返回原目录
        print(f"Error during PVS-Studio analysis: {e}")
        return None
    except Exception as e:
        os.chdir(original_dir)  # 确保返回原目录
        print(f"Unexpected error: {e}")
        return None

# 完整的分析流程
def analyze_c_project(source_directory):
    """
    完整的 C/C++ 项目分析流程。
    
    Args:
        source_directory (str): 包含 C/C++ 源代码的目录路径
    
    Returns:
        str: 分析报告内容
    """
    # 步骤1: 复制文件并添加 PVS-Studio 注释
    copied_files = copy_c_h_files_to_repo(source_directory)
    
    if not copied_files:
        return "No C/C++ files found in the specified directory."
    
    # 步骤2: 运行 PVS-Studio 分析
    report_path = run_pvs_studio_analysis()
    
    if not report_path:
        return "Failed to generate PVS-Studio analysis report."
    
    # 步骤3: 解析并返回报告
    return parse_pvs_analysis_report(report_path)

import xml.etree.ElementTree as ET
def parse_pvs_analysis_report(report_path):
    """
    解析 PVS-Studio 静态分析报告，并输出相关信息。

    """
    report_str = ""  # 用于存储分析报告的字符串

    try:
        # 解析 XML 文件
        tree = ET.parse(report_path)
        root = tree.getroot()

        # 遍历所有 <PVS-Studio_Analysis_Log> 元素
        for log_entry in root.findall('.//PVS-Studio_Analysis_Log'):
            # 提取每个问题的信息
            error_type = log_entry.find('ErrorType').text if log_entry.find('ErrorType') is not None else 'N/A'
            error_code = log_entry.find('ErrorCode').text if log_entry.find('ErrorCode') is not None else 'N/A'
            message = log_entry.find('Message').text if log_entry.find('Message') is not None else 'N/A'
            line = log_entry.find('Line').text if log_entry.find('Line') is not None else 'N/A'
            file_name = log_entry.find('File').text if log_entry.find('File') is not None else 'N/A'
            cwe_code = log_entry.find('CWECode').text if log_entry.find('CWECode') is not None else 'N/A'

            # 拼接每个问题的详细信息到字符串
            report_str += f"Error Type: {error_type}\n"
            report_str += f"Error Code: {error_code}\n"
            report_str += f"Message: {message}\n"
            report_str += f"File: {file_name}\n"
            report_str += f"Line: {line}\n"
            report_str += f"CWE Code: {cwe_code}\n"
            report_str += "-" * 50 + "\n"

    except Exception as e:
        report_str = f"Error parsing the PVS-Studio report: {e}"

    return report_str

# 使用示例
if __name__ == "__main__":
    # 指定要分析的源代码目录
    source_dir = "/path/to/your/c/project"  # 替换为实际的源代码目录
    
    # 执行完整的分析流程
    report = analyze_c_project(source_dir)
    
    # 输出分析报告
    print("PVS-Studio Analysis Report:")
    print(report)
