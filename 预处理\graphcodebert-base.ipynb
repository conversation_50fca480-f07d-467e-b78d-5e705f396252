{"cells": [{"cell_type": "code", "execution_count": 1, "id": "a8f38b53-986b-4159-8af5-12b11f06cafb", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/root/miniconda3/lib/python3.10/site-packages/torch/_utils.py:831: UserWarning: TypedStorage is deprecated. It will be removed in the future and UntypedStorage will be the only storage class. This should only matter to you if you are using storages directly.  To access UntypedStorage directly, use tensor.untyped_storage() instead of tensor.storage()\n", "  return self.fget.__get__(instance, owner)()\n", "Some weights of RobertaModel were not initialized from the model checkpoint at ../graphcodebert-base and are newly initialized: ['roberta.pooler.dense.bias', 'roberta.pooler.dense.weight']\n", "You should probably TRAIN this model on a down-stream task to be able to use it for predictions and inference.\n"]}], "source": ["import json\n", "from transformers import RobertaTokenizer, RobertaModel\n", "import torch\n", "\n", "# 加载 CodeBERT 模型\n", "MODEL_NAME = \"../graphcodebert-base\"\n", "tokenizer = RobertaTokenizer.from_pretrained(MODEL_NAME)\n", "model = RobertaModel.from_pretrained(MODEL_NAME)\n"]}, {"cell_type": "code", "execution_count": 3, "id": "5895b556-da65-420f-8388-8d29b9ab3b30", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Processing Entries: 100%|██████████| 192146/192146 [1:52:12<00:00, 28.54entry/s]  \n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "嵌入已添加并保存到文件：analyzed_files_with_graphcodebert_embeddings.json\n"]}], "source": ["from tqdm import tqdm\n", "def add_graphcodebert_embeddings_to_json(json_file_path, output_json_file_path):\n", "    \"\"\"\n", "    给 JSON 文件中的所有条目添加 GraphCodeBERT 嵌入字段，使用进度条表示处理进度。\n", "    :param json_file_path: 输入 JSON 文件路径\n", "    :param output_json_file_path: 输出 JSON 文件路径\n", "    \"\"\"\n", "    # 打开并加载 JSON 文件\n", "    with open(json_file_path, 'r', encoding='utf-8') as file:\n", "        data = json.load(file)  # 解析 JSON 数据\n", "\n", "        # 确保输入数据为列表\n", "        if not isinstance(data, list):\n", "            print(\"输入 JSON 文件的数据类型错误，期望是一个列表！\")\n", "            return\n", "\n", "        # 初始化进度条\n", "        with tqdm(total=len(data), desc=\"Processing Entries\", unit=\"entry\") as pbar:\n", "            # 遍历所有条目\n", "            for i, item in enumerate(data):\n", "                if \"code\" not in item:\n", "                    pbar.update(1)  # 无代码字段，跳过当前条目\n", "                    continue\n", "\n", "                code_snippet = item[\"code\"]  # 代码片段\n", "\n", "                # 使用 GraphCodeBERT Tokenizer 对代码进行编码\n", "                inputs = tokenizer(\n", "                    code_snippet,\n", "                    return_tensors=\"pt\",\n", "                    truncation=True,\n", "                    max_length=512  # 限制最大长度为 512 tokens\n", "                )\n", "\n", "                # 使用 GraphCodeBERT 模型生成嵌入\n", "                with torch.no_grad():  # 禁用梯度计算\n", "                    outputs = model(**inputs)\n", "                    embedding = outputs.last_hidden_state[:, 0, :].squeeze()  # 提取 [CLS] token 的嵌入\n", "\n", "                # 将嵌入转换为 Python 列表，并添加到条目\n", "                item[\"graphcodebert_embedding\"] = embedding.tolist()\n", "\n", "                # 更新进度条\n", "                pbar.update(1)\n", "\n", "    # 将更新后的数据保存到新的 JSON 文件中\n", "    with open(output_json_file_path, 'w', encoding='utf-8') as file:\n", "        json.dump(data, file, ensure_ascii=False, indent=4)\n", "        print(f\"\\n嵌入已添加并保存到文件：{output_json_file_path}\")\n", "\n", "# 示例用法\n", "input_json_file_path = \"analyzed_files.json\"  # 输入 JSON 文件路径\n", "output_json_file_path = \"analyzed_files_with_graphcodebert_embeddings.json\"  # 输出 JSON 文件路径\n", "add_graphcodebert_embeddings_to_json(input_json_file_path, output_json_file_path)"]}, {"cell_type": "code", "execution_count": null, "id": "a1b2bb1e-7fdb-4d85-b144-eab7d33d5ac8", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.8"}}, "nbformat": 4, "nbformat_minor": 5}