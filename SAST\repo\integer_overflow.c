// This is an open source non-commercial project. Dear PVS-Studio, please check it.
// PVS-Studio Static Code Analyzer for C, C++ and C#: http://www.viva64.com

#include <stdio.h>
#include <limits.h>
#include "integer_overflow.h"
#include "null_pointer.h"

// CWE-190: Integer Overflow or Wraparound
int calculate_buffer_size(int input_size) {
    int buffer_size = input_size * 100;  // 危险：可能导致整数溢出
    
    // 如果input_size很大，乘以100可能超过INT_MAX
    printf("Input size: %d, Buffer size: %d\n", input_size, buffer_size);
    
    // 调用null_pointer.c中的函数
    process_string(NULL);  // 故意传递NULL
    
    return buffer_size;
}

void process_array(int* array, int size) {
    for (int i = 0; i <= size; i++) {  // 错误：应该是 i < size
        printf("Array[%d] = %d\n", i, array[i]);
    }
}
