import os
import json
import subprocess
import logging
import shutil
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path
from dataclasses import dataclass
from neo4j import GraphDatabase
import tree_sitter
from tree_sitter import Language, Parser
import sys
sys.path.append('/root/preprocess/Treesitter')
from TreeSitterParser import TreeSitterParser, ExternalReference

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class GraphNode:
    """图节点数据结构"""
    id: str
    label: str
    properties: Dict[str, Any]

@dataclass
class GraphEdge:
    """图边数据结构"""
    source: str
    target: str
    relationship: str
    properties: Dict[str, Any]

@dataclass
class MacroDefinition:
    """宏定义数据结构"""
    name: str
    file_path: str
    line_number: int
    definition: str
    
class CtagsAnalyzer:
    """Ctags分析器，用于提取宏定义"""
    
    def __init__(self):
        self.ctags_cmd = self._find_ctags()
        
    def _find_ctags(self) -> str:
        """查找可用的ctags命令"""
        # 优先使用 universal-ctags
        for cmd in ['universal-ctags', 'ctags-universal', 'ctags']:
            if shutil.which(cmd):
                return cmd
        raise Exception("未找到ctags命令，请先安装universal-ctags")
    
    def extract_macros(self, repo_path: str) -> List[MacroDefinition]:
        """从代码仓库中提取所有宏定义"""
        macros = []
        
        try:
            # 使用ctags提取宏定义
            # -x: 以人类可读格式输出
            # --kinds-c=d: 只提取宏定义(d表示macro definitions)
            # -R: 递归处理
            cmd = [
                self.ctags_cmd,
                '-x',
                '--kinds-c=d',
                '-R',
                repo_path
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                # 解析输出
                lines = result.stdout.strip().split('\n')
                for line in lines:
                    if line:
                        # ctags -x 输出格式: name kind line file definition
                        # 根据您的示例：CONFIG_H         macro         2 config.h         #define CONFIG_H
                        parts = line.split(None, 4)  # 最多分割4次，保留定义部分
                        
                        if len(parts) >= 5:
                            name = parts[0]
                            kind = parts[1]
                            line_num = int(parts[2])
                            file_path = parts[3]
                            definition = parts[4]  # 这里包含完整的宏定义
                            
                            # 处理相对路径
                            if not os.path.isabs(file_path):
                                file_path = os.path.join(repo_path, file_path)
                            
                            macro = MacroDefinition(
                                name=name,
                                file_path=file_path,
                                line_number=line_num,
                                definition=definition
                            )
                            macros.append(macro)
                            
                logger.info(f"提取了 {len(macros)} 个宏定义")
                
            else:
                logger.error(f"ctags执行失败: {result.stderr}")
                
        except Exception as e:
            logger.error(f"提取宏定义失败: {str(e)}")
            
        return macros
    
    def _read_macro_definition(self, file_path: str, line_number: int) -> str:
        """读取宏定义的完整内容（备用方法，处理多行宏）"""
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                lines = f.readlines()
                if 0 < line_number <= len(lines):
                    # 获取定义行
                    definition = lines[line_number - 1].strip()
                    
                    # 处理多行宏定义（以反斜杠结尾）
                    current_line = line_number - 1
                    while current_line < len(lines) - 1 and lines[current_line].rstrip().endswith('\\'):
                        current_line += 1
                        definition += ' ' + lines[current_line].strip()
                    
                    return definition
                    
        except Exception as e:
            logger.warning(f"读取宏定义失败 {file_path}:{line_number} - {str(e)}")
            
        return ""

class JoernAnalyzer:
    def __init__(self, joern_path: str = "/opt/joern", workspace_path: str = "/root/preprocess/joern/repo"):
        self.joern_path = joern_path
        self.workspace_path = workspace_path
        self.output_path = os.path.join(workspace_path, "out")
        if os.path.exists(self.output_path):
            try:
                shutil.rmtree(self.output_path)
                logger.info(f"清理输出目录完成: {self.output_path}")
            except Exception as e:
                logger.warning(f"清理输出目录失败: {str(e)}")
        os.makedirs(self.workspace_path, exist_ok=True)
        
    def analyze_repository(self, repo_path: str) -> str:
        """使用Joern分析代码仓库"""
        try:
            logger.info(f"开始分析代码仓库: {repo_path}")
            
            # 切换到工作目录
            original_dir = os.getcwd()
            os.chdir(self.workspace_path)
            
            # 1. 使用joern-parse分析代码
            cmd = ["joern-parse", repo_path]
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
            
            if result.returncode != 0:
                raise Exception(f"Joern parse失败: {result.stderr}")
            
            logger.info("Joern parse完成")
            
            # 2. 导出为Neo4j CSV格式
            export_cmd = [
                "joern-export",
                "--repr=all",
                "--format=neo4jcsv"
            ]
            
            result = subprocess.run(export_cmd, capture_output=True, text=True, timeout=300)
            
            if result.returncode != 0:
                raise Exception(f"Joern export失败: {result.stderr}")
            
            logger.info("Joern export完成")
            
            # 恢复原目录
            os.chdir(original_dir)
            
            return self.output_path
            
        except Exception as e:
            logger.error(f"代码分析失败: {str(e)}")
            if 'original_dir' in locals():
                os.chdir(original_dir)
            raise
    
    def copy_csv_to_neo4j_import(self, neo4j_import_path: str = "/var/lib/neo4j/import"):
        """复制CSV文件到Neo4j导入目录"""
        try:
            # 确保Neo4j导入目录存在
            os.makedirs(neo4j_import_path, exist_ok=True)
            for file in os.listdir(neo4j_import_path):
                if file.endswith("_data.csv"):
                    file_path = os.path.join(neo4j_import_path, file)
                    try:
                        os.remove(file_path)
                        logger.info(f"已删除旧文件: {file}")
                    except Exception as e:
                        logger.warning(f"删除文件失败 {file}: {str(e)}")

            # 复制所有_data.csv文件
            import glob
            data_files = glob.glob(os.path.join(self.output_path, "*_data.csv"))
            
            for file_path in data_files:
                dest_path = os.path.join(neo4j_import_path, os.path.basename(file_path))
                shutil.copy2(file_path, dest_path)
                logger.info(f"已复制: {os.path.basename(file_path)}")
            
            return True
            
        except Exception as e:
            logger.error(f"复制CSV文件失败: {str(e)}")
            raise

class Neo4jManager:
    """Neo4j图数据库管理器"""
    
    def __init__(self, uri: str = "bolt://localhost:7687", 
                 username: str = "neo4j", password: str = "password"):
        self.driver = GraphDatabase.driver(uri, auth=(username, password))
        self.username = username
        self.password = password
        
    def __enter__(self):
        return self
        
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close()
        
    def close(self):
        """关闭数据库连接"""
        if self.driver:
            self.driver.close()
    
    def clear_database(self):
        """清空数据库"""
        with self.driver.session() as session:
            session.run("MATCH (n) DETACH DELETE n")
            logger.info("数据库已清空")
    
    def import_from_cypher_files(self, joern_output_path: str):
        """使用cypher-shell从CSV文件导入数据"""
        try:
            # 设置cypher-shell环境变量
            env = os.environ.copy()
            env['NEO4J_USERNAME'] = self.username
            env['NEO4J_PASSWORD'] = self.password
            
            # 导入节点
            node_files = Path(joern_output_path).glob("nodes_*_cypher.csv")
            for node_file in sorted(node_files):
                logger.info(f"导入节点文件: {node_file.name}")
                cmd = ["cypher-shell", "--file", str(node_file)]
                result = subprocess.run(cmd, capture_output=True, text=True, env=env)
                
                if result.returncode != 0:
                    logger.warning(f"导入节点失败 {node_file.name}: {result.stderr}")
                else:
                    logger.info(f"成功导入节点文件: {node_file.name}")
            
            # 导入边
            edge_files = Path(joern_output_path).glob("edges_*_cypher.csv")
            for edge_file in sorted(edge_files):
                logger.info(f"导入边文件: {edge_file.name}")
                cmd = ["cypher-shell", "--file", str(edge_file)]
                result = subprocess.run(cmd, capture_output=True, text=True, env=env)
                
                if result.returncode != 0:
                    logger.warning(f"导入边失败 {edge_file.name}: {result.stderr}")
                else:
                    logger.info(f"成功导入边文件: {edge_file.name}")
            
            logger.info("所有数据导入完成")
            
        except Exception as e:
            logger.error(f"导入数据失败: {str(e)}")
            raise
    
    def import_macro_definitions(self, macros: List[MacroDefinition]):
        """将宏定义导入到Neo4j"""
        try:
            with self.driver.session() as session:
                # 创建MACRO节点的Cypher查询
                create_macro_query = """
                UNWIND $macros AS macro
                CREATE (m:MACRO {
                    name: macro.name,
                    filename: macro.file_path,
                    lineNumber: macro.line_number,
                    definition: macro.definition,
                    code: macro.definition
                })
                """
                
                # 准备数据
                macro_data = []
                for macro in macros:
                    macro_data.append({
                        'name': macro.name,
                        'file_path': macro.file_path,
                        'line_number': macro.line_number,
                        'definition': macro.definition
                    })
                
                # 批量创建节点
                if macro_data:
                    session.run(create_macro_query, macros=macro_data)
                    logger.info(f"成功导入 {len(macro_data)} 个宏定义到Neo4j")
                
                # 创建与FILE节点的关系
                create_relation_query = """
                MATCH (m:MACRO), (f:FILE)
                WHERE m.filename = f.name
                CREATE (f)-[:DEFINES_MACRO]->(m)
                """
                
                session.run(create_relation_query)
                logger.info("创建宏定义与文件的关系完成")
                
        except Exception as e:
            logger.error(f"导入宏定义失败: {str(e)}")
            raise

class DependencyQueryService:
    """依赖查询服务"""
    
    def __init__(self, neo4j_manager: Neo4jManager):
        self.neo4j = neo4j_manager
    
    def query_external_functions(self, function_names: List[str], exclude_file: str = None) -> Dict[str, List[Dict]]:
        """查询外部函数的定义位置"""
        results = {}
        
        query = """
        MATCH (m:METHOD)
        WHERE m.NAME = $func_name 
        AND ($exclude_file IS NULL OR NOT m.FILENAME = $exclude_file)
        RETURN m.NAME as name, 
            m.FILENAME as file_path, 
            m.LINE_NUMBER as line_number,
            m.LINE_NUMBER_END as line_number_end,
            m.CODE as definition_code,
            m.SIGNATURE as signature,
            m.FULL_NAME as full_name
        ORDER BY m.FILENAME, m.LINE_NUMBER
        LIMIT 10
        """
        
        with self.neo4j.driver.session() as session:
            for func_name in function_names:
                result = session.run(query, func_name=func_name, exclude_file=exclude_file)
                definitions = [record.data() for record in result]
                if definitions:
                    results[func_name] = definitions
        
        return results
    
    def query_external_variables(self, variable_names: List[str], exclude_file: str = None) -> Dict[str, List[Dict]]:
        """查询外部变量的定义位置"""
        results = {}
        
        # 直接查询所有IDENTIFIER节点，只返回TYPE_FULL_NAME
        identifier_query = """
        MATCH (i:IDENTIFIER)
        WHERE i.NAME = $var_name
        AND i.LINE_NUMBER IS NOT NULL
        AND i.TYPE_FULL_NAME IS NOT NULL
        RETURN DISTINCT i.TYPE_FULL_NAME as type_full_name
        LIMIT 10
        """
        
        # 如果需要排除特定文件
        identifier_with_exclude_query = """
        MATCH (i:IDENTIFIER)<-[:AST*]-(file:FILE)
        WHERE i.NAME = $var_name
        AND i.LINE_NUMBER IS NOT NULL
        AND i.TYPE_FULL_NAME IS NOT NULL
        AND NOT file.NAME = $exclude_file
        RETURN DISTINCT i.TYPE_FULL_NAME as type_full_name
        LIMIT 10
        """
        
        # 也查询LOCAL节点的TYPE_FULL_NAME
        local_query = """
        MATCH (l:LOCAL)
        WHERE l.NAME = $var_name
        AND l.TYPE_FULL_NAME IS NOT NULL
        RETURN DISTINCT l.TYPE_FULL_NAME as type_full_name
        LIMIT 10
        """
        
        with self.neo4j.driver.session() as session:
            for var_name in variable_names:
                type_names = set()  # 使用集合去重
                
                if exclude_file:
                    # 如果需要排除文件，使用带文件路径的查询
                    result = session.run(identifier_with_exclude_query, 
                                    var_name=var_name, 
                                    exclude_file=exclude_file)
                else:
                    # 否则直接查询所有IDENTIFIER节点
                    result = session.run(identifier_query, var_name=var_name)
                
                # 收集IDENTIFIER的类型
                for record in result:
                    if record['type_full_name']:
                        type_names.add(record['type_full_name'])
                
                # 查询LOCAL节点
                result = session.run(local_query, var_name=var_name)
                for record in result:
                    if record['type_full_name']:
                        type_names.add(record['type_full_name'])
                
                # 将结果转换为列表格式
                if type_names:
                    results[var_name] = [{'type_full_name': t} for t in sorted(type_names)]
        
        return results

    
    def query_external_types(self, type_names: List[str], exclude_file: str = None) -> Dict[str, List[Dict]]:
        """查询外部类型的定义位置"""
        results = {}
        
        # 查询类型声明
        type_query = """
        MATCH (t:TYPE_DECL)
        WHERE (t.name = $type_name OR t.fullName = $type_name OR t.fullName = 'struct ' + $type_name)
        AND ($exclude_file IS NULL OR NOT t.filename = $exclude_file)
        RETURN t.name as name, t.fullName as full_name, t.filename as file_path,
               t.lineNumber as line_number, t.code as definition_code
        ORDER BY t.filename, t.lineNumber
        LIMIT 10
        """
        
        with self.neo4j.driver.session() as session:
            for type_name in type_names:
                result = session.run(type_query, type_name=type_name, exclude_file=exclude_file)
                definitions = [record.data() for record in result]
                if definitions:
                    results[type_name] = definitions
        
        return results
    
    def query_external_macros(self, macro_names: List[str], exclude_file: str = None) -> Dict[str, List[Dict]]:
        """查询外部宏的定义位置"""
        results = {}
        
        # 更新查询，使用MACRO节点
        query = """
        MATCH (m:MACRO)
        WHERE m.name = $macro_name
        AND ($exclude_file IS NULL OR NOT m.filename = $exclude_file)
        RETURN m.name as name, m.filename as file_path, 
               m.lineNumber as line_number, m.definition as definition_code
        ORDER BY m.filename, m.lineNumber
        LIMIT 10
        """
        
        with self.neo4j.driver.session() as session:
            for macro_name in macro_names:
                result = session.run(query, macro_name=macro_name, exclude_file=exclude_file)
                definitions = [record.data() for record in result]
                if definitions:
                    results[macro_name] = definitions
        
        return results
    
    def query_file_dependencies(self, file_path: str) -> Dict[str, Any]:
        """查询文件的依赖关系"""
        dependencies = {
            "imports": [],
            "imported_by": [],
            "calls_to": [],
            "called_by": []
        }
        
        # 查询文件包含关系
        include_query = """
        MATCH (f:FILE {name: $file_path})-[:INCLUDES]->(included:FILE)
        RETURN included.name as included_file
        """
        
        # 查询被包含关系
        included_by_query = """
        MATCH (f:FILE)-[:INCLUDES]->(target:FILE {name: $file_path})
        RETURN f.name as including_file
        """
        
        with self.neo4j.driver.session() as session:
            # 获取包含的文件
            result = session.run(include_query, file_path=file_path)
            dependencies["imports"] = [r["included_file"] for r in result]
            
            # 获取包含此文件的文件
            result = session.run(included_by_query, file_path=file_path)
            dependencies["imported_by"] = [r["including_file"] for r in result]
        
        return dependencies

class CodeRepositoryGraphGenerator:
    """代码仓库图生成器"""
    
    def __init__(self, joern_path: str = "/opt/joern", 
                 workspace_path: str = "/root/preprocess/joern/repo",
                 neo4j_uri: str = "bolt://localhost:7687",
                 neo4j_user: str = "neo4j", 
                 neo4j_password: str = "password",
                 neo4j_import_path: str = "/var/lib/neo4j/import",
                 tree_sitter_language_path: str = "/opt/tree-sitter/languages/tree-sitter-c/build/my-languages.so"):
        
        self.joern_analyzer = JoernAnalyzer(joern_path, workspace_path)
        self.tree_sitter_parser = TreeSitterParser(tree_sitter_language_path)
        self.neo4j_manager = Neo4jManager(neo4j_uri, neo4j_user, neo4j_password)
        self.query_service = DependencyQueryService(self.neo4j_manager)
        self.ctags_analyzer = CtagsAnalyzer()
        self.neo4j_import_path = neo4j_import_path
    
    def process_repository(self, repo_path: str, clear_db: bool = True) -> bool:
        """处理代码仓库并导入Neo4j"""
        try:
            logger.info("开始处理代码仓库...")
            
            # 清空数据库（可选）
            if clear_db:
                self.neo4j_manager.clear_database()
            
            # 1. 使用Joern分析代码并导出CSV
            output_path = self.joern_analyzer.analyze_repository(repo_path)
            
            # 2. 复制CSV文件到Neo4j导入目录
            self.joern_analyzer.copy_csv_to_neo4j_import(self.neo4j_import_path)
            
            # 3. 使用cypher-shell导入数据
            self.neo4j_manager.import_from_cypher_files(output_path)
            
            # 4. 使用ctags提取宏定义
            logger.info("开始使用ctags提取宏定义...")
            macros = self.ctags_analyzer.extract_macros(repo_path)
            
            # 5. 将宏定义导入Neo4j
            if macros:
                self.neo4j_manager.import_macro_definitions(macros)
                logger.info(f"已导入 {len(macros)} 个宏定义到Neo4j")
            else:
                logger.warning("未找到任何宏定义")
            
            logger.info("代码仓库处理完成")
            return True
            
        except Exception as e:
            logger.error(f"处理失败: {str(e)}")
            return False
    
    def analyze_file_dependencies(self, file_path: str) -> Dict[str, Any]:
        """分析文件的外部依赖并查询定义位置"""
        result = {
            "file_path": file_path,
            "external_dependencies": {
                "functions": {},
                "variables": {},
                "types": {},
                "macros": {}
            },
            "definitions_found": {
                "functions": {},
                "variables": {},
                "types": {},
                "macros": {}
            },
            "file_dependencies": {},
            "summary": {}
        }
        
        try:
            # 1. 使用Tree-Sitter分析外部依赖
            dependencies = self.tree_sitter_parser.analyze_external_dependencies(file_path)
            
            # 提取各类依赖的名称
            func_names = [dep['name'] for dep in dependencies['external_functions']]
            var_names = [dep['name'] for dep in dependencies['external_variables']]
            type_names = [dep['name'] for dep in dependencies['external_types']]
            macro_names = [dep['name'] for dep in dependencies['external_macros']]
            
            # 保存Tree-Sitter的分析结果
            result["external_dependencies"]["functions"] = dependencies['external_functions']
            result["external_dependencies"]["variables"] = dependencies['external_variables']
            result["external_dependencies"]["types"] = dependencies['external_types']
            result["external_dependencies"]["macros"] = dependencies['external_macros']
            
            # 2. 在Neo4j中查询这些依赖的定义位置
            if func_names:
                func_defs = self.query_service.query_external_functions(func_names, exclude_file=file_path)
                result["definitions_found"]["functions"] = func_defs
            
            if var_names:
                var_defs = self.query_service.query_external_variables(var_names, exclude_file=file_path)
                result["definitions_found"]["variables"] = var_defs
            
            if type_names:
                type_defs = self.query_service.query_external_types(type_names, exclude_file=file_path)
                result["definitions_found"]["types"] = type_defs
            
            if macro_names:
                macro_defs = self.query_service.query_external_macros(macro_names, exclude_file=file_path)
                result["definitions_found"]["macros"] = macro_defs
            
            # 3. 查询文件级别的依赖关系
            file_deps = self.query_service.query_file_dependencies(file_path)
            result["file_dependencies"] = file_deps
            
            # 4. 生成摘要
            result["summary"] = {
                "total_external_functions": len(func_names),
                "total_external_variables": len(var_names),
                "total_external_types": len(type_names),
                "total_external_macros": len(macro_names),
                "functions_with_definitions": len(result["definitions_found"]["functions"]),
                "variables_with_definitions": len(result["definitions_found"]["variables"]),
                "types_with_definitions": len(result["definitions_found"]["types"]),
                "macros_with_definitions": len(result["definitions_found"]["macros"]),
                "imported_files": len(file_deps.get("imports", [])),
                "imported_by_files": len(file_deps.get("imported_by", []))
            }
            
        except Exception as e:
            result["error"] = str(e)
            logger.error(f"分析文件依赖失败: {str(e)}")
        
        return result
    
    def generate_dependency_report(self, file_path: str) -> str:
        """生成依赖分析报告"""
        analysis = self.analyze_file_dependencies(file_path)
        
        report = f"=== 依赖分析报告 ===\n"
        report += f"文件: {file_path}\n\n"
        
        # 摘要信息
        if "summary" in analysis:
            summary = analysis["summary"]
            report += "=== 摘要 ===\n"
            report += f"外部函数: {summary['total_external_functions']} (已找到定义: {summary['functions_with_definitions']})\n"
            report += f"外部变量: {summary['total_external_variables']} (已找到定义: {summary['variables_with_definitions']})\n"
            report += f"外部类型: {summary['total_external_types']} (已找到定义: {summary['types_with_definitions']})\n"
            report += f"外部宏: {summary['total_external_macros']} (已找到定义: {summary['macros_with_definitions']})\n"
            report += f"导入文件数: {summary['imported_files']}\n"
            report += f"被导入次数: {summary['imported_by_files']}\n\n"
        
        # 外部函数
        if analysis["external_dependencies"]["functions"]:
            report += "=== 外部函数 ===\n"
            for func in analysis["external_dependencies"]["functions"]:
                report += f"\n{func['name']} (line {func['line']})\n"
                report += f"  上下文: {func['context']}\n"
                
                # 显示找到的定义
                if func['name'] in analysis["definitions_found"]["functions"]:
                    defs = analysis["definitions_found"]["functions"][func['name']]
                    report += "  定义:\n"
                    for d in defs[:3]:  # 最多显示3个定义
                        report += f"    - {d['file_path']}:{d['line_number']}\n"
                        if 'definition_code' in d:
                            report += f"      {d['definition_code']}\n"
                else:
                    report += "  定义位置: 未找到\n"
        
        # 外部变量
        if analysis["external_dependencies"]["variables"]:
            report += "\n=== 外部变量 ===\n"
            for var in analysis["external_dependencies"]["variables"]:
                report += f"\n{var['name']} (line {var['line']})\n"
                
                if var['name'] in analysis["definitions_found"]["variables"]:
                    defs = analysis["definitions_found"]["variables"][var['name']]
                    report += "  类型:\n"
                    for d in defs:
                        if d['type_full_name'] != "ANY":
                            report += f"    - {d['type_full_name']}\n"
                else:
                    report += "  类型: 未找到\n"
        
        # 外部宏
        if analysis["external_dependencies"]["macros"]:
            report += "\n=== 外部宏 ===\n"
            for macro in analysis["external_dependencies"]["macros"]:
                report += f"\n{macro['name']} (line {macro['line']})\n"
                
                if macro['name'] in analysis["definitions_found"]["macros"]:
                    defs = analysis["definitions_found"]["macros"][macro['name']]
                    report += "  定义:\n"
                    for d in defs[:3]:  # 最多显示3个定义
                        report += f"    - {d['file_path']}:{d['line_number']}\n"
                        if 'definition_code' in d:
                            report += f"      {d['definition_code']}\n"
                else:
                    report += "  定义位置: 未找到\n"
        
        # 文件依赖
        if analysis["file_dependencies"]["imports"]:
            report += "\n=== 包含的文件 ===\n"
            for imp in analysis["file_dependencies"]["imports"]:
                report += f"  - {imp}\n"
        
        return report
    
    def close(self):
        """关闭连接"""
        self.neo4j_manager.close()


def main():
    generator = CodeRepositoryGraphGenerator(
        joern_path="/opt/joern",
        workspace_path="/root/preprocess/joern/repo",
        neo4j_uri="bolt://localhost:7687",
        neo4j_user="neo4j",
        neo4j_password="password",
        neo4j_import_path="/var/lib/neo4j/import",
        tree_sitter_language_path="/opt/tree-sitter/languages/tree-sitter-c/build/my-languages.so"
    )
    
    try:
        # 处理代码仓库
        repo_path = "/root/preprocess/joern/repo/testRepo"
        success = generator.process_repository(repo_path,True)

        if success:
            # 分析特定文件的依赖
            file_path = "/root/preprocess/joern/repo/testRepo/main.c"
            
            # 生成详细的依赖分析结果
            dependencies = generator.analyze_file_dependencies(file_path)
            
            # 输出JSON格式结果
            print("\n=== JSON格式结果 ===")
            print(json.dumps(dependencies, indent=2, ensure_ascii=False))
            
            # 生成可读的报告
            print("\n=== 文本格式报告 ===")
            report = generator.generate_dependency_report(file_path)
            print(report)
            
    finally:
        generator.close()

if __name__ == "__main__":
    main()

