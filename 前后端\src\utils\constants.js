export const VULNERABILITY_TYPES = {
    BUFFER_OVERFLOW: {
        name: 'Buffer Overflow',
        description: '缓冲区溢出漏洞',
        severity: 'high',
        cwe: 'CWE-119'
    },
    SQL_INJECTION: {
        name: 'SQL Injection',
        description: 'SQL注入漏洞',
        severity: 'high',
        cwe: 'CWE-89'
    },
    XSS: {
        name: 'Cross-site Scripting (XSS)',
        description: '跨站脚本漏洞',
        severity: 'medium',
        cwe: 'CWE-79'
    },
    COMMAND_INJECTION: {
        name: 'Command Injection',
        description: '命令注入漏洞',
        severity: 'high',
        cwe: 'CWE-78'
    },
    PATH_TRAVERSAL: {
        name: 'Path Traversal',
        description: '路径遍历漏洞',
        severity: 'medium',
        cwe: 'CWE-22'
    },
    WEAK_CRYPTO: {
        name: 'Weak Cryptography',
        description: '弱加密算法',
        severity: 'medium',
        cwe: 'CWE-327'
    },
    MEMORY_LEAK: {
        name: 'Memory Leak',
        description: '内存泄漏',
        severity: 'low',
        cwe: 'CWE-401'
    },
    RACE_CONDITION: {
        name: 'Race Condition',
        description: '竞态条件',
        severity: 'medium',
        cwe: 'CWE-362'
    }
}

export const ANALYSIS_MODULES = {
    SAST: {
        id: 'sast',
        name: '静态安全扫描',
        description: '基于规则的静态代码分析',
        icon: 'el-icon-search'
    },
    CONTEXT: {
        id: 'context',
        name: '外部依赖解析',
        description: '分析代码的外部依赖和上下文',
        icon: 'el-icon-connection'
    },
    VECTOR: {
        id: 'vector',
        name: '特征向量提取',
        description: '将代码转换为语义向量',
        icon: 'el-icon-cpu'
    },
    HISTORICAL: {
        id: 'historical',
        name: '历史漏洞匹配',
        description: '与历史漏洞数据库进行匹配',
        icon: 'el-icon-time'
    },
    LLM: {
        id: 'llm',
        name: 'LLM综合分析',
        description: '使用大语言模型进行深度分析',
        icon: 'el-icon-magic-stick'
    }
}

export const FILE_EXTENSIONS = {
    C: ['c', 'h'],
    CPP: ['cpp', 'cc', 'cxx', 'hpp', 'hh', 'hxx'],
    JAVA: ['java'],
    PYTHON: ['py'],
    JAVASCRIPT: ['js', 'jsx'],
    TYPESCRIPT: ['ts', 'tsx'],
    GO: ['go'],
    RUST: ['rs'],
    RUBY: ['rb'],
    PHP: ['php']
}

export const SEVERITY_LEVELS = {
    CRITICAL: {
        value: 'critical',
        label: '严重',
        color: '#8B0000',
        score: 10
    },
    HIGH: {
        value: 'high',
        label: '高危',
        color: '#F56C6C',
        score: 8
    },
    MEDIUM: {
        value: 'medium',
        label: '中危',
        color: '#E6A23C',
        score: 5
    },
    LOW: {
        value: 'low',
        label: '低危',
        color: '#F0D20E',
        score: 3
    },
    INFO: {
        value: 'info',
        label: '信息',
        color: '#909399',
        score: 1
    }
}
