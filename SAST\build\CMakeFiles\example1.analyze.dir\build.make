# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /root/preprocess/SAST

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /root/preprocess/SAST/build

# Utility rule file for example1.analyze.

# Include any custom commands dependencies for this target.
include CMakeFiles/example1.analyze.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/example1.analyze.dir/progress.make

CMakeFiles/example1.analyze: pvs-studio-cmake-example-1

example1.analyze: CMakeFiles/example1.analyze
example1.analyze: CMakeFiles/example1.analyze.dir/build.make
.PHONY : example1.analyze

# Rule to build all files generated by this target.
CMakeFiles/example1.analyze.dir/build: example1.analyze
.PHONY : CMakeFiles/example1.analyze.dir/build

CMakeFiles/example1.analyze.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/example1.analyze.dir/cmake_clean.cmake
.PHONY : CMakeFiles/example1.analyze.dir/clean

CMakeFiles/example1.analyze.dir/depend:
	cd /root/preprocess/SAST/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /root/preprocess/SAST /root/preprocess/SAST /root/preprocess/SAST/build /root/preprocess/SAST/build /root/preprocess/SAST/build/CMakeFiles/example1.analyze.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : CMakeFiles/example1.analyze.dir/depend

